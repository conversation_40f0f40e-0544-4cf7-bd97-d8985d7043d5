"use strict";

const selfGameModel = require("../models/selfGame.model");

class SelfGameRepo {
    static async getListSelfGames(filter, attributes, order, limit) {
        return await selfGameModel.findAll({
            where: filter,
            limit: limit,
            order: order,
            attributes: attributes
        })
    }

    static async getListSelfGamesPagination(filter, attributes, order, page, limit) {
        const {count, rows} = await selfGameModel.findAndCountAll({
            where: filter,
            attributes: attributes,
            order: order,
            offset: (page - 1) * limit,
            limit: limit
        })

        return {
            total_items: count,
            items_per_page: limit,
            current_page: page,
            total_pages: Math.ceil(count / limit),
            items: rows
        };
    }

    static async getSelfGameDetail(filter, include, attributes) {
        return await selfGameModel.findOne({
            where: filter,
            include: include,
            attributes: attributes
        })
    }

    static async createSelfGame(data) {
        const result = await selfGameModel.create(data);
        return result.id;
    }

    static async bulkCreateSelfGames(datas) {
        return await selfGameModel.bulkCreate(datas);
    }

    static async updateSelfGame(model, data, filter = {}) {
        await model.update(data, {
            where: filter
        });
    }

    static async updateSelfGameWithConditions(data, filter = {}) {
        await selfGameModel.update(data, {
            where: filter
        });
    }

    static async deleteSelfGame(filter) {
        await selfGameModel.destroy({
            where: filter
        });
    }
}

module.exports = SelfGameRepo;