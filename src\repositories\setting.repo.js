"use strict";

const settingModel = require("../models/setting.model");

class SettingRepo {
    static async getListSettings(filter, attributes, order, limit) {
        return await settingModel.findAll({
            where: filter,
            attributes: attributes,
            limit: limit,
            order: order
        });
    }

    static async getSettingDetail(filter, attributes) {
        return await settingModel.findOne({
            where: filter,
            attributes: attributes
        });
    }

    static async createSettingBulk(datas) {
        await settingModel.bulkCreate(datas);
    }

    static async updateSettingWithCondition(data, filter = {}) {
        await settingModel.update(data, {
            where: filter
        });
    }

    static async deleteSetting(filter) {
        await settingModel.destroy({
            where: filter
        });
    }
}

module.exports = SettingRepo;