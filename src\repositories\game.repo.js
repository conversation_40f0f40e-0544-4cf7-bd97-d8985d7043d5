"use strict";

const gameModel = require("../models/game.model");

class GameRepo {
    static async getListGames(filter, attributes, order, limit) {
        return await gameModel.findAll({
            where: filter,
            limit: limit,
            order: order,
            attributes: attributes
        })
    }

    static async getListGamesPagination(filter, attributes, order, page, limit) {
        const {count, rows} = await gameModel.findAndCountAll({
            where: filter,
            attributes: attributes,
            order: order,
            offset: (page - 1) * limit,
            limit: limit
        })

        return {
            total_items: count,
            items_per_page: limit,
            current_page: page,
            total_pages: Math.ceil(count / limit),
            items: rows
        };
    }

    static async getGameDetail(filter, include, attributes) {
        return await gameModel.findOne({
            where: filter,
            include: include,
            attributes: attributes
        })
    }

    static async createGame(data) {
        const result = await gameModel.create(data);
        return result.id;
    }

    static async bulkCreateGames(datas) {
        return await gameModel.bulkCreate(datas);
    }

    static async updateGame(model, data, filter = {}) {
        await model.update(data, {
            where: filter
        });
    }

    static async updateGameWithConditions(data, filter = {}) {
        await gameModel.update(data, {
            where: filter
        });
    }

    static async deleteGame(filter) {
        await gameModel.destroy({
            where: filter
        });
    }
}

module.exports = GameRepo;