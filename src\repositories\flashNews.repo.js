"use strict";

const flashNewsModel = require("../models/flashNews.model");

class FlashNewsRepo {
    static async getListFlashNews(filter, attributes, order, limit) {
        return await flashNewsModel.findAll({
            where: filter,
            limit: limit,
            order: order,
            attributes: attributes
        })
    }

    static async getFlashNewsDetail(filter, attributes) {
        return await flashNewsModel.findOne({
            where: filter,
            attributes: attributes
        })
    }

    static async createFlashNews(data) {
        const result = await flashNewsModel.create(data);
        return result.id;
    }

    static async updateFlashNews(model, data, filter = {}) {
        await model.update(data, {
            where: filter
        });
    }

    static async updateFlashNewsWithConditions(data, filter = {}) {
        const result = await flashNewsModel.update(data, {
            where: filter,
        });
    }
}

module.exports = FlashNewsRepo;