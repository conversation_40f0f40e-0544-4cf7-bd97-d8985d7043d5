"use strict";

const Queue = require('bull');

const updateGameReportQueue = new Queue('updateGameReportQueue', {
    prefix: `${process.env.REDIS_KEY_PREFIX}bull`,
    redis: {
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT,
        password: process.env.REDIS_PASSWORD,
    },
});

updateGameReportQueue.process(async (job, done) => {
    const {fid, code, uid, month, year} = job.data;

    try {
        await require('../services/website/v1/game.service').updateGameReport(fid, code, uid, month, year);
        done();
    } catch (error) {
        console.error(`Failed to update game report ${fid}/${code}/${month}/${year}::`, error.message);
        done(new Error(`Failed to update game report ${fid}/${code}/${month}/${year}:: ` + error.message));
    }
});

// Logging và xử lý sự kiện
// sendEmailQueue.on('completed', (job) => {
//     console.log(`Job ${job.id} has been completed`);
// });

updateGameReportQueue.on('failed', (job, err) => {
    console.error(`Job ${job.id} failed with error: ${err.message}`);
});

updateGameReportQueue.on('stalled', (job) => {
    console.warn(`Job ${job.id} has stalled and will be reprocessed`);
});

// sendEmailQueue.on('delayed', (job) => {
//     console.log(`Job ${job.id} has been delayed`);
// });

module.exports = {
    updateGameReportQueue,
};