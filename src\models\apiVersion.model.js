"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const apiVersionModel = sequelize.define('api_versions', {
    api: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    client: {
        type: DataTypes.STRING(10),
        allowNull: false,
        primaryKey: true,
    },
}, {
    tableName: 'api_versions',
    timestamps: false,
    underscored: true,
    freezeTableName: true,
    id: false,
});

module.exports = apiVersionModel;