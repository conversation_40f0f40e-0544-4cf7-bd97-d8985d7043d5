"use strict";

const express = require('express');
const commonRoutes = require("../website/common.router");
const gameRoutes = require("../website/game.router");
const h5GameRoutes = require("../website/h5Game.router");
const bannerRoutes = require("../website/banner.router");
const bannerV2Routes = require("../website/bannerV2.router");
const playlistRoutes = require("../website/playlist.router");
const videoRoutes = require("../website/video.router");
const newsRoutes = require("../website/news.router");
const trackingRoutes = require("../website/tracking.router");
const {verifyClient} = require("../../middlewares/website/verifyClient.mid");

const router = express.Router()

// Common
router.use('/', commonRoutes);

// Game
router.use('/game', gameRoutes);
router.use('/game-h5', h5GameRoutes);

// system
router.use('/banner', bannerRoutes);
router.use('/banner-v2', [bannerV2Routes]);
router.use('/playlist', [verifyClient, playlistRoutes]);
router.use('/video', [verifyClient, videoRoutes]);

// news
router.use('/news', newsRoutes);

// tracking
router.use('/tracking', [verifyClient, trackingRoutes]);

module.exports = router;