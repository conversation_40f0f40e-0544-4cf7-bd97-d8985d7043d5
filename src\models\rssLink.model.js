"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const rssLinkModel = sequelize.define('rss_links', {
    link: {
        type: DataTypes.STRING,
        primaryKey: true,
        allowNull: false,
    },
    percent: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    get_data_for_home: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
    },
    status: {
        type: DataTypes.INTEGER,
        allowNull: false,
    }
}, {
    id: false,
    timestamps: false,
    underscored: true,
    tableName: 'rss_links'
})

module.exports = rssLinkModel