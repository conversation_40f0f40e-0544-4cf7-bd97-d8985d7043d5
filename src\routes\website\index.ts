import { Router } from 'express';
import { verifyClient } from '@/middlewares/auth.middleware';

// Import route modules
import gameRoutes from './game.routes';
import h5GameRoutes from './h5Game.routes';
import trackingRoutes from './tracking.routes';

const router = Router();

/**
 * Website API Routes
 */

// Game routes
router.use('/game', gameRoutes);
router.use('/game-h5', h5GameRoutes);

// System routes
// router.use('/banner', bannerRoutes);
// router.use('/banner-v2', bannerV2Routes);
// router.use('/playlist', verifyClient, playlistRoutes);
// router.use('/video', verifyClient, videoRoutes);

// News routes
// router.use('/news', newsRoutes);

// Tracking routes (with client verification)
router.use('/tracking', trackingRoutes);

// Common routes (root level)
// These would include RSS, app settings, location, etc.
// router.use('/', commonRoutes);

export default router;
