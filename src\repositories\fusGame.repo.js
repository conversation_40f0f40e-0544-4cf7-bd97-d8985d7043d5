"use strict";

const fusGameModel = require("../models/fusGame.model");

class fusGameRepo {
    static async getListFusGames(filter, attributes, order, limit) {
        return await fusGameModel.findAll({
            where: filter,
            limit: limit,
            order: order,
            attributes: attributes
        })
    }

    static async getListFusGamesPagination(filter, attributes, order, page, limit) {
        const {count, rows} = await fusGameModel.findAndCountAll({
            where: filter,
            attributes: attributes,
            order: order,
            offset: (page - 1) * limit,
            limit: limit
        })

        return {
            total_items: count,
            items_per_page: limit,
            current_page: page,
            total_pages: Math.ceil(count / limit),
            items: rows
        };
    }

    static async getFusGameDetail(filter, attributes) {
        return await fusGameModel.findOne({
            where: filter,
            attributes: attributes
        })
    }

    static async createFusGame(data) {
        const result = await fusGameModel.create(data);
        return result.id;
    }

    static async updateFusGame(model, data, filter = {}) {
        await model.update(data, {
            where: filter
        });
    }

    static async updateFusGameWithConditions(data, filter = {}) {
        await fusGameModel.update(data, {
            where: filter
        });
    }
}

module.exports = fusGameRepo;