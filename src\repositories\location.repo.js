"use strict";

const locationModel = require("../models/location.model");

class LocationRepo {
    static async getListLocationsPagination(filter, include, attributes, order, page, limit) {
        const {count, rows} = await locationModel.findAndCountAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            offset: (page - 1) * limit,
            order: order
        });

        return {
            total_items: count,
            items_per_page: limit,
            total_pages: Math.ceil(count / limit),
            current_page: page,
            items: rows
        };
    }

    static async getListLocations(filter, include, attributes, order, limit) {
        return await locationModel.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            order: order
        });
    }

    static async getLocationDetail(filter, attributes) {
        return await locationModel.findOne({
            where: filter,
            attributes: attributes
        })
    }

    static async createLocation(data) {
        const result = await locationModel.create(data);
        return result.id;
    }

    static async updateLocation(model, data) {
        await model.update(data);
    }
}

module.exports = LocationRepo;