"use strict";

const linkReportModel = require("../models/linkReport.model");

class LinkReportRepo {
    static async getListLinkReports(filter, include, attributes, group, order, limit) {
        return await linkReportModel.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            group: group,
            order: order,
            limit: limit,
        })
    }

    static async getLinkReportDetail(filter, attributes) {
        return await linkReportModel.findOne({
            where: filter,
            attributes: attributes
        })
    }

    static async createLinkReport(data) {
        return linkReportModel.create(data);
    }

    static async updateLinkReport(model, data) {
        await model.update(data);
    }
}

module.exports = LinkReportRepo;