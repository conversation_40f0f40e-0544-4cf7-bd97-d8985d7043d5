// Error handling middlewares
export * from './error.middleware';

// Authentication and authorization middlewares
export * from './auth.middleware';

// Validation middlewares
export * from './validation.middleware';

// Logging middlewares
export * from './logging.middleware';

// Re-export commonly used middleware combinations
import { 
  getApiVersion, 
  verifyClient, 
  verifyClientV2, 
  requestTimer 
} from './auth.middleware';
import { requestLogger, errorLogger } from './logging.middleware';
import { notFound, errorHandler } from './error.middleware';

/**
 * Common middleware combinations
 */
export const commonMiddlewares = {
  // Basic middlewares applied to all routes
  basic: [
    requestTimer,
    requestLogger,
    getApiVersion,
  ],
  
  // Authenticated routes
  authenticated: [
    requestTimer,
    requestLogger,
    getApiVersion,
    verifyClient,
  ],
  
  // Enhanced authenticated routes
  authenticatedV2: [
    requestTimer,
    requestLogger,
    getApiVersion,
    verifyClientV2,
  ],
  
  // Error handling middlewares (applied at the end)
  errorHandling: [
    errorLogger,
    notFound,
    errorHandler,
  ],
};
