"use strict";

const bannerReportModel = require("../models/bannerReport.model");

class BannerReportRepo {
    static async getListBannerReports(filter, include, attributes, group, order, limit) {
        return await bannerReportModel.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            group: group,
            order: order,
            limit: limit,
        })
    }

    static async getBannerReportDetail(filter, attributes) {
        return await bannerReportModel.findOne({
            where: filter,
            attributes: attributes
        })
    }

    static async createBannerReport(data) {
        return bannerReportModel.create(data);
    }

    static async updateBannerReport(model, data) {
        await model.update(data);
    }
}

module.exports = BannerReportRepo;