import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest, AsyncHandler } from '@/types/common';

/**
 * Async handler wrapper to catch errors and pass them to error middleware
 */
export const asyncHandler = (fn: AsyncHandler) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Version-aware async handler that dynamically loads controllers based on API version
 */
export const asyncVersionHandler = (
  controllerName: string,
  methodName: string,
  controllerPath: string = 'controllers/website'
) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const controller = getController(controllerName, controllerPath, req.api_version || 1);
      return asyncHandler(controller[methodName])(req, res, next);
    } catch (err) {
      next(err);
    }
  };
};

/**
 * Dynamically load controller based on version
 */
const getController = (name: string, dir: string, version: number) => {
  const fs = require('fs');
  const path = require('path');
  
  const controllerPath = path.join(process.cwd(), 'src', dir, `v${version}`, `${name}.controller.ts`);
  
  if (!fs.existsSync(controllerPath)) {
    // Fallback to .js extension for compiled files
    const jsPath = path.join(process.cwd(), 'dist', dir, `v${version}`, `${name}.controller.js`);
    if (!fs.existsSync(jsPath)) {
      throw new Error(`Controller not found: ${name} v${version}`);
    }
    return require(jsPath);
  }
  
  return require(controllerPath);
};

/**
 * Type-safe async handler with request/response type checking
 */
export const typedAsyncHandler = <TReq = any, TRes = any>(
  fn: (req: AuthenticatedRequest & { body: TReq }, res: Response) => Promise<TRes>
) => {
  return asyncHandler(async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const result = await fn(req as AuthenticatedRequest & { body: TReq }, res);
    if (result && !res.headersSent) {
      res.json(result);
    }
  });
};
