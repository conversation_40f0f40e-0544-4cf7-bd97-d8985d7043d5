"use strict";

const bannerTimeSlotModel = require("../models/bannerTimeSlot.model");

class BannerTimeSlotRepo {
    static async getListBannerTimeSlots(filter, include, attributes, order = null, limit = null) {
        return await bannerTimeSlotModel.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            order: order
        });
    }

    static async createBannerTimeSlot(data) {
        const result = await bannerTimeSlotModel.create(data);
        return result.id;
    }

    static async createBannerTimeSlotBulk(datas) {
        await bannerTimeSlotModel.bulkCreate(datas);
    }

    static async updateBannerTimeSlot(model, data) {
        await model.update(data);
    }

    static async deleteBannerTimeSlot(filter) {
        await bannerTimeSlotModel.destroy({
            where: filter
        });
    }
}

module.exports = BannerTimeSlotRepo;