"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const bannerReportModel = sequelize.define('banner_report', {
    banner_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    fid: {
        type: DataTypes.STRING,
        allowNull: false,
        primaryKey: true,
    },
    type: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    month: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    year: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    data: {
        type: DataTypes.TEXT,
        allowNull: false,
    },
    total_open: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    timestamps: false,
    underscored: true,
    indexes: [
        {
            fields: ['fid', 'month', 'year'],
        },
    ],
    tableName: 'banner_report'
})

module.exports = bannerReportModel