"use strict";

const {OK} = require("../../../utils/successResponse");
const feedbackService = require("../../../services/website/v1/feedback.service");

class FeedbackController {
    sendFeedback = async (req, res, next) => {
        new OK({
            message: 'Send feedback successfully.',
            metadata: await feedbackService.sendFeedback(req)
        }).send(res)
    }
}

module.exports = new FeedbackController();