"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const linkReportModel = sequelize.define('link_report', {
    link: {
        type: DataTypes.STRING,
        allowNull: false,
        primaryKey: true,
    },
    type: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    fid: {
        type: DataTypes.STRING,
        allowNull: false,
        primaryKey: true,
    },
    month: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    year: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    total_open: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    timestamps: false,
    underscored: true,
    indexes: [
        {
            fields: ['link', 'type', 'fid', 'month', 'year'],
        },
    ],
    tableName: 'link_report'
})

module.exports = linkReportModel