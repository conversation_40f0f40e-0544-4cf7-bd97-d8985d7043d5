import { z } from 'zod';

// Base pagination schema
export const paginationSchema = z.object({
  page: z.string().optional().transform(val => val ? Math.max(1, parseInt(val, 10)) : 1),
  limit: z.string().optional().transform(val => {
    const parsed = val ? parseInt(val, 10) : 20;
    return Math.min(Math.max(1, parsed), 100); // Limit between 1-100
  }),
});

// Base query parameters schema
export const baseQuerySchema = paginationSchema.extend({
  search: z.string().optional(),
  status: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['ASC', 'DESC']).optional().default('DESC'),
});

// FID parameter schema (commonly used)
export const fidParamSchema = z.object({
  fid: z.string().transform(val => parseInt(val, 10)),
});

// UID parameter schema
export const uidParamSchema = z.object({
  uid: z.string().transform(val => parseInt(val, 10)),
});

// Combined FID and UID params
export const fidUidParamsSchema = z.object({
  fid: z.string().transform(val => parseInt(val, 10)),
  uid: z.string().transform(val => parseInt(val, 10)),
});

// ID parameter schema
export const idParamSchema = z.object({
  id: z.string().transform(val => parseInt(val, 10)),
});

// Headers schema for API versioning
export const apiVersionHeaderSchema = z.object({
  'x-client-version': z.string().optional(),
  'x-timestamp': z.string().optional(),
  'x-signature': z.string().optional(),
});

// Base response schema
export const baseResponseSchema = z.object({
  status: z.enum(['success', 'error']),
  code: z.number(),
  message: z.string(),
  metadata: z.any().optional(),
  errors: z.record(z.string()).optional(),
});

// Success response schema
export const successResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    status: z.literal('success'),
    code: z.number(),
    message: z.string(),
    metadata: dataSchema,
  });

// Error response schema
export const errorResponseSchema = z.object({
  status: z.literal('error'),
  code: z.number(),
  message: z.string(),
  errors: z.record(z.string()).optional(),
});

// Pagination response schema
export const paginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    items: z.array(itemSchema),
    total: z.number(),
    page: z.number(),
    limit: z.number(),
    totalPages: z.number(),
  });

// Date range schema
export const dateRangeSchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
});

// File upload schema
export const fileUploadSchema = z.object({
  fieldname: z.string(),
  originalname: z.string(),
  encoding: z.string(),
  mimetype: z.string(),
  buffer: z.instanceof(Buffer),
  size: z.number(),
});

// Validation middleware schema
export const validationOptionsSchema = z.object({
  body: z.any().optional(),
  query: z.any().optional(),
  params: z.any().optional(),
  headers: z.any().optional(),
});

// Type exports
export type PaginationParams = z.infer<typeof paginationSchema>;
export type BaseQueryParams = z.infer<typeof baseQuerySchema>;
export type FidParam = z.infer<typeof fidParamSchema>;
export type UidParam = z.infer<typeof uidParamSchema>;
export type FidUidParams = z.infer<typeof fidUidParamsSchema>;
export type IdParam = z.infer<typeof idParamSchema>;
export type ApiVersionHeaders = z.infer<typeof apiVersionHeaderSchema>;
export type BaseResponse = z.infer<typeof baseResponseSchema>;
export type DateRange = z.infer<typeof dateRangeSchema>;
export type FileUpload = z.infer<typeof fileUploadSchema>;
export type ValidationOptions = z.infer<typeof validationOptionsSchema>;
