"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const locationModel = sequelize.define('locations', {
    id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
    },
    fid: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    fuid: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    region_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    city_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    name: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    address: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    latest_version: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    force_update: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
    },
    status: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    type: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
    },
    created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: true,
    },
    updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    deleted_at: {
        type: DataTypes.DATE,
        allowNull: true,
    },
    deleted_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
    }
}, {
    timestamps: false,
    underscored: true,
    defaultScope: {
        attributes: {exclude: ['created_by', 'updated_at', 'updated_by', 'deleted_at', 'deleted_by']}
    },
    tableName: 'locations'
});


module.exports = locationModel;