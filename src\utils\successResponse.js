"use strict";

const {statusCodes, reasonPhrases} = require("../enums/index")

class SuccessResponse {
    constructor({message, code = statusCodes.OK, reason = reasonPhrases.OK, metadata = null}) {
        this.status = "success"
        this.code = code
        this.message = !message ? reason : message
        this.metadata = metadata
    }

    send(res, header = {}) {
        return res.status(this.code).json(this)
    }
}

class OK extends SuccessResponse {
    constructor({message, metadata}) {
        super({message, metadata})
    }
}

class CREATED extends SuccessResponse {
    constructor(message, status = statusCodes.CREATED, reason = reasonPhrases.CREATED, metadata) {
        super({message, status, reason, metadata})
    }
}

module.exports = {
    OK,
    CREATED
}