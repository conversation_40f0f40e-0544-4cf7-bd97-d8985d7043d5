import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import rateLimit from 'express-rate-limit';
import multer from 'multer';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Import middlewares
import { 
  requestLogger, 
  performanceLogger, 
  securityLogger 
} from '@/middlewares/logging.middleware';
import { 
  notFound, 
  errorHandler, 
  requestTimer 
} from '@/middlewares/error.middleware';
import { sanitizeInput } from '@/middlewares/validation.middleware';

// Import routes
import routes from '@/routes';

// Create Express app
const app = express();

// Trust proxy (for load balancers, reverse proxies)
app.set('trust proxy', 1);

/**
 * Security Middlewares
 */
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
}));

/**
 * CORS Configuration
 */
const corsOptions: cors.CorsOptions = {
  origin: (origin, callback) => {
    const allowedDomains = [
      /^https:\/\/.*\.oeg\.vn$/,
      'https://talenthouse.vn',
      'https://dev.talenthouse.vn',
      'http://localhost:5173', // Local development
      'http://localhost:3002', // Local development
      'http://127.0.0.1:55808', // FUS test
      'http://localhost:55808', // FUS test
      'https://menu.fusoft.vn', // FUS test
      'https://dev-cms.fusoft.vn',
      'https://dev-client-cms.fusoft.vn',
    ];

    const isAllowed = !origin || allowedDomains.some(pattern =>
      typeof pattern === 'string'
        ? pattern === origin
        : pattern.test(origin)
    );

    if (isAllowed) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'x-client-version',
    'x-timestamp',
    'x-signature',
  ],
};

app.use(cors(corsOptions));

/**
 * Rate Limiting
 */
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Limit each IP to 1000 requests per windowMs
  message: {
    status: 'error',
    code: 429,
    message: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

/**
 * Body Parsing Middlewares
 */
app.use(compression());
app.use(cookieParser());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Multer for handling multipart/form-data
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
});
app.use(upload.none());

/**
 * Custom Middlewares
 */
app.use(requestTimer);
app.use(requestLogger);
app.use(performanceLogger);
app.use(securityLogger);
app.use(sanitizeInput);

/**
 * Routes
 */
app.use('/', routes);

/**
 * Error Handling Middlewares
 */
app.use(notFound);
app.use(errorHandler);

/**
 * Graceful Shutdown Handler
 */
const gracefulShutdown = (signal: string) => {
  console.log(`Received ${signal}. Starting graceful shutdown...`);
  
  // Close server
  process.exit(0);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

export default app;
