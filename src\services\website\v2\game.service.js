"use strict";

const {Op, fn, col} = require("sequelize");
const redisService = require("../../redis.service");
const bannerV2ClientService = require("../v1/banner.service");
const gameReportRepo = require("../../../repositories/gameReport.repo");
const gameModel = require("../../../models/game.model");

class GameService {
    static async getListTopGames(req) {
        const fid = req.params.fid || 0
        const limit = req.query.limit ? parseInt(req.query.limit) : 12;

        const redisKey = `cache:top_games:${fid}`

        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        let result = []

        const [
            topGames,
            iconGameHot4,
            iconGameHot5,
            iconGameHot6
        ] = await Promise.all([
            gameReportRepo.getListGameReports(
                {
                    fid,
                    code: {
                        [Op.and]: [
                            {[Op.notLike]: 'self%'},
                            {[Op.notLike]: 'gh5%'},
                        ],
                    }
                },
                [
                    {
                        model: gameModel,
                        attributes: ['id', 'code', 'name', 'logo'],
                        required: true
                    },
                    // {
                    //     model: selfGameModel,
                    //     where: {fid},
                    //     attributes: ['id', 'fid', 'code', 'name', 'logo'],
                    //     required: false
                    // },
                    // {
                    //     model: h5GameModel,
                    //     where: {
                    //         status: defaultStatus.ACTIVE,
                    //         deleted_at: null,
                    //         deleted_by: null,
                    //     },
                    //     attributes: ['id', 'code', 'name', 'logo', 'link'],
                    //     required: false
                    // }
                ],
                ['code', [fn('SUM', col('total_open')), 'total_open']],
                ['code', 'game.id', 'game.code',
                    // 'self_game.id', 'self_game.code',
                    // 'h5_game.id', 'h5_game.code'
                ],
                [[fn('SUM', col('total_open')), 'DESC']],
                limit
            ),
            bannerV2ClientService.getBannerV2ByPosition(fid, 6, 1),
            bannerV2ClientService.getBannerV2ByPosition(fid, 7, 1),
            bannerV2ClientService.getBannerV2ByPosition(fid, 8, 1),
        ]);

        const hotGameIcons = [iconGameHot4, iconGameHot5, iconGameHot6]
        const hotGameCodes = hotGameIcons.filter(item => item !== null).map(item => {
            if (item?.redirect_link) {
                return item.redirect_link.code
            }
        })

        result = topGames.filter(item => !hotGameCodes.includes(item.code)).map(top => ({
            code: top.code,
            // name: top.game?.name || top.self_game?.name || top.h5_game?.name || null,
            // logo: top.game?.logo || top.self_game?.logo || top.h5_game?.logo || null,
            name: top.game?.name,
            logo: top.game?.logo,
            // link: top.h5_game?.link || undefined,
        }));

        // Insert icons at positions
        hotGameIcons.forEach((icon, idx) => {
            if (icon) {
                const formatData = {
                    id: icon.id,
                    image: icon.image,
                    redirect_link: icon.redirect_link,
                }
                const insertIndex = 3 + idx;
                if (result.length > insertIndex) {
                    result.splice(insertIndex, 0, formatData);
                } else {
                    result.push(formatData);
                }
            }
        });

        result = result.slice(0, limit + hotGameIcons?.length ?? 0)

        // Nếu không có icon game hot thì mới cache (để đảm bảo tracking chuẩn)
        if (!iconGameHot4 && !iconGameHot5 && !iconGameHot6) {
            await redisService.pushKeyToRedis(redisKey, JSON.stringify(result), 1800) // 30p
        }

        return result
    }
}

module.exports = GameService