"use strict";

const bannersV2Model = require("../models/bannerV2.model");

class BannerV2Repo {
    static async getListBanners(filter, include, attributes, order = null, limit = null) {
        return await bannersV2Model.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            order: order
        });
    }

    static async getListBannersPagination(filter, include, attributes, order, page, limit) {
        const {count, rows} = await bannersV2Model.findAndCountAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            offset: (page - 1) * limit,
            order: order
        });
        return {
            total_items: count,
            items_per_page: limit,
            total_pages: Math.ceil(count / limit),
            current_page: page,
            items: rows
        };
    }

    static async getBannerDetail(filter, include = null, attributes = []) {
        return await bannersV2Model.findOne({
            where: filter,
            include: include,
            attributes: attributes
        })
    }

    static async createBanner(data) {
        const result = await bannersV2Model.create(data);
        return result.id;
    }

    static async updateBanner(model, data) {
        await model.update(data);
    }

    static async updateBannerWithCondition(data, filter = {}) {
        await bannersV2Model.update(data, {
            where: filter
        });
    }
}

module.exports = BannerV2Repo;