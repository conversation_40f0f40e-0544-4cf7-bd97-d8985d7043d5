import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '@/types/common';

/**
 * Request logging interface
 */
interface RequestLog {
  method: string;
  url: string;
  ip: string;
  userAgent?: string;
  timestamp: Date;
  duration?: number;
  statusCode?: number;
  contentLength?: number;
  apiVersion?: number;
  userId?: string;
  sessionId?: string;
}

/**
 * Logger class for handling different log levels
 */
class Logger {
  private logLevel: string;

  constructor() {
    this.logLevel = process.env.LOG_LEVEL || 'info';
  }

  private shouldLog(level: string): boolean {
    const levels = ['error', 'warn', 'info', 'debug'];
    const currentLevelIndex = levels.indexOf(this.logLevel);
    const requestedLevelIndex = levels.indexOf(level);
    return requestedLevelIndex <= currentLevelIndex;
  }

  private formatLog(level: string, message: string, meta?: any): string {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      ...(meta && { meta }),
    };
    return JSON.stringify(logEntry);
  }

  error(message: string, meta?: any): void {
    if (this.shouldLog('error')) {
      console.error(this.formatLog('error', message, meta));
    }
  }

  warn(message: string, meta?: any): void {
    if (this.shouldLog('warn')) {
      console.warn(this.formatLog('warn', message, meta));
    }
  }

  info(message: string, meta?: any): void {
    if (this.shouldLog('info')) {
      console.info(this.formatLog('info', message, meta));
    }
  }

  debug(message: string, meta?: any): void {
    if (this.shouldLog('debug')) {
      console.debug(this.formatLog('debug', message, meta));
    }
  }
}

export const logger = new Logger();

/**
 * Request logging middleware
 */
export const requestLogger = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  const startTime = Date.now();
  
  // Create request log entry
  const requestLog: RequestLog = {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip || req.connection.remoteAddress || 'unknown',
    userAgent: req.get('User-Agent'),
    timestamp: new Date(),
    apiVersion: req.api_version,
  };

  // Log request start
  logger.info('Request started', {
    method: requestLog.method,
    url: requestLog.url,
    ip: requestLog.ip,
    userAgent: requestLog.userAgent,
    apiVersion: requestLog.apiVersion,
  });

  // Override res.end to capture response details
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any, cb?: any) {
    const duration = Date.now() - startTime;
    
    // Update request log with response details
    requestLog.duration = duration;
    requestLog.statusCode = res.statusCode;
    requestLog.contentLength = res.get('Content-Length') ? 
      parseInt(res.get('Content-Length')!, 10) : undefined;

    // Log request completion
    const logLevel = res.statusCode >= 400 ? 'error' : 'info';
    const logMessage = `Request completed - ${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms`;
    
    if (logLevel === 'error') {
      logger.error(logMessage, requestLog);
    } else {
      logger.info(logMessage, requestLog);
    }

    // In real implementation, you might want to:
    // 1. Send logs to external service (e.g., ELK stack, CloudWatch)
    // 2. Store in database for analytics
    // 3. Send to monitoring service

    // Call original end method
    originalEnd.call(this, chunk, encoding, cb);
  };

  next();
};

/**
 * Error logging middleware
 */
export const errorLogger = (
  error: Error,
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  const errorLog = {
    message: error.message,
    stack: error.stack,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date(),
    apiVersion: req.api_version,
    body: req.body,
    query: req.query,
    params: req.params,
  };

  logger.error('Request error occurred', errorLog);

  // In real implementation, you might want to:
  // 1. Send error to error tracking service (e.g., Sentry)
  // 2. Alert monitoring systems
  // 3. Store in error database

  next(error);
};

/**
 * Performance monitoring middleware
 */
export const performanceLogger = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  const startTime = process.hrtime.bigint();
  const startMemory = process.memoryUsage();

  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    const memoryDiff = {
      rss: endMemory.rss - startMemory.rss,
      heapUsed: endMemory.heapUsed - startMemory.heapUsed,
      heapTotal: endMemory.heapTotal - startMemory.heapTotal,
    };

    // Log performance metrics
    logger.debug('Performance metrics', {
      method: req.method,
      url: req.originalUrl,
      duration: `${duration.toFixed(2)}ms`,
      statusCode: res.statusCode,
      memoryDiff,
    });

    // Alert if request is slow
    if (duration > 1000) { // More than 1 second
      logger.warn('Slow request detected', {
        method: req.method,
        url: req.originalUrl,
        duration: `${duration.toFixed(2)}ms`,
        statusCode: res.statusCode,
      });
    }
  });

  next();
};

/**
 * Security logging middleware
 */
export const securityLogger = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  // Log suspicious activities
  const suspiciousPatterns = [
    /\.\.\//,  // Path traversal
    /<script/i, // XSS attempts
    /union.*select/i, // SQL injection
    /javascript:/i, // JavaScript injection
  ];

  const checkSuspicious = (value: string): boolean => {
    return suspiciousPatterns.some(pattern => pattern.test(value));
  };

  const checkObject = (obj: any, path: string = ''): void => {
    if (typeof obj === 'string' && checkSuspicious(obj)) {
      logger.warn('Suspicious request detected', {
        type: 'potential_attack',
        path,
        value: obj,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        url: req.originalUrl,
      });
    } else if (typeof obj === 'object' && obj !== null) {
      Object.entries(obj).forEach(([key, value]) => {
        checkObject(value, path ? `${path}.${key}` : key);
      });
    }
  };

  // Check URL, query, and body for suspicious content
  checkObject(req.originalUrl, 'url');
  checkObject(req.query, 'query');
  checkObject(req.body, 'body');

  next();
};
