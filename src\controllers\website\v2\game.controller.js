"use strict";

const {OK} = require("../../../utils/successResponse");
const gameV1Service = require("../../../services/website/v1/game.service");
const gameV2Service = require("../../../services/website/v2/game.service");

class GameController {
    getListGames = async (req, res, next) => {
        new OK({
            message: 'Get list games successfully.',
            metadata: await gameV1Service.getListGames(req)
        }).send(res)
    }

    getListTopGames = async (req, res, next) => {
        new OK({
            message: 'Get list top games successfully.',
            metadata: await gameV2Service.getListTopGames(req)
        }).send(res)
    }

    getListUserGames = async (req, res, next) => {
        new OK({
            message: 'Get list user games successfully.',
            metadata: await gameV1Service.getListUserGames(req)
        }).send(res)
    }

    getGameDetail = async (req, res, next) => {
        new OK({
            message: 'Get game detail successfully.',
            metadata: await gameV1Service.getGameDetail(req)
        }).send(res)
    }

    openGame = async (req, res, next) => {
        new OK({
            message: 'Open game successfully.',
            metadata: await gameV1Service.openGame(req)
        }).send(res)
    }

    createGame = async (req, res, next) => {
        new OK({
            message: 'Create game successfully.',
            metadata: await gameV1Service.createGame(req)
        }).send(res)
    }

    getListRss = async (req, res, next) => {
        new OK({
            message: 'Get list rss successfully.',
            metadata: await gameV1Service.getListRss(req)
        }).send(res)
    }
}

module.exports = new GameController();