import { Router } from 'express';
import { asyncHandler } from '@/utils/asyncHandler';
import { validateQuery, validateParams } from '@/middlewares/validation.middleware';
import {
  h5GameQuerySchema,
  h5GameUserParamsSchema,
} from '@/schemas/h5Game.schema';

const controllerName = 'h5Game';
const router = Router();

/**
 * H5 Game Routes
 */

// GET /game-h5 - Get list of H5 games
router.get(
  '/',
  validateQuery(h5GameQuerySchema),
  asyncHandler(async (req, res, next) => {
    const { asyncVersionHandler } = await import('@/utils/asyncHandler');
    return asyncVersionHandler(controllerName, 'getListH5Games')(req, res, next);
  })
);

// GET /game-h5/category - Get H5 game categories
router.get(
  '/category',
  asyncHandler(async (req, res, next) => {
    const { asyncVersionHandler } = await import('@/utils/asyncHandler');
    return asyncVersionHandler(controllerName, 'getListH5GameCategories')(req, res, next);
  })
);

// GET /game-h5/trending - Get trending H5 games
router.get(
  '/trending',
  validateQuery(h5GameQuerySchema),
  asyncHandler(async (req, res, next) => {
    const { asyncVersionHandler } = await import('@/utils/asyncHandler');
    return asyncVersionHandler(controllerName, 'getListTrendingH5Games')(req, res, next);
  })
);

// GET /game-h5/:fid/:uid - Get user H5 games
router.get(
  '/:fid/:uid',
  validateParams(h5GameUserParamsSchema),
  validateQuery(h5GameQuerySchema),
  asyncHandler(async (req, res, next) => {
    const { asyncVersionHandler } = await import('@/utils/asyncHandler');
    return asyncVersionHandler(controllerName, 'getListUserH5Games')(req, res, next);
  })
);

export default router;
