"use strict";

const {query, body} = require('express-validator');

const newGameRequest = [
    body('fid').notEmpty().withMessage('FID is required'),
    body('data').notEmpty().withMessage('New game codes is required')
        .custom(value => {
            if (!Array.isArray(value)) {
                throw new Error('New game codes must be an array');
            }
            return value.length > 0;
        })
        .custom(value => {
            if (!Array.isArray(value)) {
                throw new Error('New game codes must be an array');
            }
            if (value.length === 0) {
                throw new Error('New game codes cannot be an empty array');
            }

            const isValid = value.every(item =>
                typeof item === 'object' &&
                item !== null &&
                typeof item.code === 'string' &&
                typeof item.name === 'string'
            );

            if (!isValid) {
                throw new Error('New game codes incorrect format');
            }

            return true;
        })
];

const rssRequest = [
    query('link').notEmpty().withMessage('Link rss is required'),
];

module.exports = {
    newGameRequest,
    rssRequest,
}