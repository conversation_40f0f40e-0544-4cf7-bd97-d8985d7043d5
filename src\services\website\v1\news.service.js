"use strict";

const Parser = require("rss-parser");
const parser = new Parser({
    headers: {
        Accept: 'application/rss+xml, application/xml',
    },
});
const redisService = require("../../redis.service");
const flashNewsRepo = require("../../../repositories/flashNews.repo");
const rssLinkRepo = require("../../../repositories/rssLink.repo");
const settingRepo = require("../../../repositories/setting.repo");
const {defaultStatus, booleanStatus} = require("../../../enums/objStatus");
const {Op} = require("sequelize");
const moment = require("moment-timezone");
const {sendMessage} = require("../../../loggers/teleBot");

class NewsService {
    static async getListNews(req) {
        const fid = req.query.fid || 0;
        const offset = req.query.offset ? parseInt(req.query.offset) : 0;
        const limit = req.query.limit ? parseInt(req.query.limit) : 10;

        const redisKey = `cache:news:${fid}:${limit}:${offset}`

        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        const setting = await settingRepo.getSettingDetail({
            fid,
            key: 'total_news'
        }, ['value'])
        const totalNews = setting?.dataValues?.value || 30
        if (offset >= totalNews) {
            return []
        }

        const rssLinks = await rssLinkRepo.getListRssLinks({
            status: defaultStatus.ACTIVE,
        }, ['link', 'percent'], [['percent', 'DESC']], null);

        if (!rssLinks.length) return [];

        let listNews = []

        let articlesList = await Promise.all(rssLinks.map(async ({link, percent}) => {
            const redisKey = `cache:rss:${link}`;

            const articlesData = await redisService.getKey(redisKey);

            const articles = articlesData ? JSON.parse(articlesData).sort((a, b) => b.pubDate - a.pubDate) : [];

            return {articles, percent};
        }));
        articlesList = articlesList.filter(item => item.articles.length !== 0)

        let totalPercent = articlesList.reduce((acc, curr) => acc + curr.percent, 0); // Tổng phần trăm

        let articlesDistribution = articlesList.map(articleSource => {
            return {
                ...articleSource, count: Math.round(totalNews * (articleSource.percent / totalPercent))
            };
        });

        let index = 0;
        for (let i = 0; i < articlesDistribution.length; i++) {
            let articleSource = articlesDistribution[i];
            let articles = articleSource.articles;

            if (index >= totalNews) break;

            let articleCount = 0
            for (let j = 0; j < articles.length; j++) {
                if (index >= totalNews || articleCount >= articleSource.count) break;

                let currentArticle = articles[j];

                let isDuplicate = listNews.some(item => item.title === currentArticle.title || item.thumbnail === currentArticle.thumbnail || item.link === currentArticle.link);

                if (!isDuplicate && currentArticle && currentArticle.thumbnail && currentArticle.title && currentArticle.link) {
                    listNews.push({
                        thumbnail: currentArticle.thumbnail,
                        title: currentArticle.title,
                        link: currentArticle.link,
                        pubDate: moment(currentArticle.pubDate).format('YYYY-MM-DD HH:mm:ss'),
                    })

                    index++;
                    articleCount++;
                }
            }
        }

        listNews = listNews.sort((a, b) => new Date(b.pubDate) - new Date(a.pubDate)).slice(offset, Math.min(offset + limit, totalNews))

        await redisService.pushKeyToRedis(redisKey, JSON.stringify(listNews), 900) // 15p

        return listNews;
    }

    // Không áp dụng tỉ lệ cho từng link
    static async getListFlashNewsV1() {
        // Lấy danh sách flash news
        const flashNews = await flashNewsRepo.getListFlashNews(
            null,
            ['thumbnail', 'title', 'link', 'sort'],
            [['sort', 'ASC']],
            null
        );

        // Lọc các mục bị thiếu thông tin
        const missingItems = flashNews.filter(item => !item.thumbnail || !item.title || !item.link);
        if (missingItems.length === 0) return flashNews; // Nếu không có mục nào thiếu, trả về luôn

        // Lấy danh sách RSS links
        const rssLinks = await rssLinkRepo.getListRssLinks(
            {status: defaultStatus.ACTIVE, get_data_for_home: booleanStatus.TRUE},
            ['link', 'percent'],
            [['percent', 'DESC']],
            null
        );

        if (!rssLinks.length) return flashNews; // Nếu không có RSS links, trả về danh sách ban đầu

        // Lấy dữ liệu bài viết từ Redis
        const redisKeys = rssLinks.map(({link}) => `cache:rss:${link}`);
        const redisData = await redisService.getKeys(redisKeys); // Giả sử hàm này trả về nhiều keys cùng lúc

        // Parse và gộp danh sách bài viết từ Redis
        const articlesList = redisData
            .map(data => (data ? JSON.parse(data) : [])) // Parse JSON hoặc trả về mảng rỗng
            .flat()
            .sort((a, b) => new Date(b.pubDate) - new Date(a.pubDate)) // Sắp xếp theo ngày đăng bài (pubDate)
            .slice(0, missingItems.length); // Giới hạn số bài viết bằng số mục thiếu

        if (!articlesList.length) return flashNews; // Nếu không có bài viết nào, trả về danh sách ban đầu

        // Điền thông tin bị thiếu vào flash news
        let articleIndex = 0;
        flashNews.forEach(item => {
            if ((!item.thumbnail || !item.title || !item.link) && articleIndex < articlesList.length) {
                const {thumbnail, title, link} = articlesList[articleIndex++];
                Object.assign(item, {thumbnail, title, link});
            }
        });

        return flashNews;
    }

    static async getListFlashNewsV2() {
        const flashNews = await flashNewsRepo.getListFlashNews(null, ['thumbnail', 'title', 'link', 'sort'], [['sort', 'ASC']], null);

        const missingItems = flashNews.filter(item => !item.thumbnail || !item.title || !item.link);
        const totalMissing = missingItems.length;

        if (totalMissing === 0) return flashNews;

        const rssLinks = await rssLinkRepo.getListRssLinks({
            status: defaultStatus.ACTIVE, get_data_for_home: booleanStatus.TRUE
        }, ['link', 'percent'], [['percent', 'DESC']], null);

        if (!rssLinks.length) return flashNews;

        const articlesList = await Promise.all(rssLinks.map(async ({link, percent}) => {
            const redisKey = `cache:rss:${link}`;

            const articlesData = await redisService.getKey(redisKey);

            const articles = articlesData ? JSON.parse(articlesData).sort((a, b) => b.pubDate - a.pubDate) : [];

            return {articles, percent};
        }));

        let totalPercent = articlesList.reduce((acc, curr) => acc + curr.percent, 0); // Tổng phần trăm

        let articlesDistribution = articlesList.map(articleSource => {
            return {
                ...articleSource, count: Math.round(totalMissing * (articleSource.percent / totalPercent))
            };
        });

        let index = 0;
        for (let i = 0; i < articlesDistribution.length; i++) {
            let articleSource = articlesDistribution[i];
            let articles = articleSource.articles;

            if (index >= totalMissing) break;

            let articleCount = 0
            for (let j = 0; j < articles.length; j++) {
                if (index >= totalMissing || articleCount >= articleSource.count) break;

                let currentArticle = articles[j];

                let isDuplicate = flashNews.some(item => item.title === currentArticle.title || item.thumbnail === currentArticle.thumbnail || item.link === currentArticle.link);

                if (!isDuplicate && currentArticle) {
                    const missingItemIndex = flashNews.findIndex(el => el.sort === missingItems[index].sort);

                    flashNews[missingItemIndex].thumbnail = currentArticle.thumbnail;
                    flashNews[missingItemIndex].title = currentArticle.title;
                    flashNews[missingItemIndex].link = currentArticle.link;

                    index++;
                    articleCount++;
                }
            }
        }

        return flashNews;
    }

    static async getListFlashNewsV3() {
        const redisKey = `cache:flash_news`

        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        // Lấy danh sách flashNews
        const flashNews = await flashNewsRepo.getListFlashNews(null, ['thumbnail', 'title', 'link', 'sort'], [['sort', 'ASC']], null);

        // Lọc ra những bài viết bị thiếu thông tin
        const missingItems = flashNews.filter(item => !item.thumbnail || !item.title || !item.link);
        const totalMissing = missingItems.length;

        if (totalMissing === 0) return flashNews;

        // Lấy danh sách RSS links
        const rssLinks = await rssLinkRepo.getListRssLinks({
            status: defaultStatus.ACTIVE, get_data_for_home: booleanStatus.TRUE
        }, ['link', 'percent'], [['percent', 'DESC']], null);

        if (!rssLinks.length) return flashNews;

        // Sử dụng Redis để lấy dữ liệu bài viết từ tất cả các RSS links cùng lúc
        const redisKeys = rssLinks.map(({link}) => `cache:rss:${link}`);
        const redisData = await redisService.getKeys(redisKeys); // Giả sử bạn có hàm getKeys để lấy nhiều keys một lần

        // Tạo danh sách bài viết từ dữ liệu Redis
        const articlesList = rssLinks.map((rssLink, index) => {
            const articlesData = redisData[index];
            const articles = articlesData ? JSON.parse(articlesData).sort((a, b) => new Date(b.pubDate) - new Date(a.pubDate)) : [];
            return {articles, percent: rssLink.percent};
        }).filter(item => item.articles.length !== 0);

        // Tính tổng phần trăm để phân chia số lượng bài viết từ các nguồn
        let totalPercent = articlesList.reduce((acc, curr) => acc + curr.percent, 0);

        // Phân phối bài viết dựa trên phần trăm, làm tròn lên bằng Math.ceil
        let articlesDistribution = articlesList.map(articleSource => {
            return {
                ...articleSource, count: Math.ceil(totalMissing * (articleSource.percent / totalPercent))
            };
        });

        // Tính tổng số bài viết đã được phân phối
        let distributedCount = articlesDistribution.reduce((acc, curr) => acc + curr.count, 0);

        // Nếu phân phối vượt quá số lượng thiếu, điều chỉnh lại
        let excessCount = distributedCount - totalMissing;
        if (excessCount > 0) {
            // Sắp xếp các nguồn theo percent từ thấp đến cao
            articlesDistribution.sort((a, b) => a.percent - b.percent);

            // Giảm số lượng bài viết dư thừa từ các nguồn percent thấp nhất
            for (let i = 0; i < articlesDistribution.length && excessCount > 0; i++) {
                if (articlesDistribution[i].count > 0) {
                    articlesDistribution[i].count--;
                    excessCount--;
                }
            }
        }

        // Sử dụng Set để kiểm tra trùng lặp nhanh hơn
        const existingTitles = new Set(flashNews.map(item => item.title));
        const existingThumbnails = new Set(flashNews.map(item => item.thumbnail));
        const existingLinks = new Set(flashNews.map(item => item.link));

        // Đảo lại các bài có percent cao hơn lên đầu
        articlesDistribution.sort((a, b) => b.percent - a.percent);

        let index = 0;
        const validArticles = [];
        for (let i = 0; i < articlesDistribution.length; i++) {
            let articleSource = articlesDistribution[i];
            let articles = articleSource.articles;

            if (index >= totalMissing) break;

            let articleCount = 0;
            for (let j = 0; j < articles.length; j++) {
                if (index >= totalMissing || articleCount >= articleSource.count) break;

                let currentArticle = articles[j];

                // Kiểm tra xem bài viết có bị trùng lặp không
                let isDuplicate = existingTitles.has(currentArticle.title) || existingThumbnails.has(currentArticle.thumbnail) || existingLinks.has(currentArticle.link);

                if (!isDuplicate && currentArticle && currentArticle.thumbnail && currentArticle.title && currentArticle.link) {
                    validArticles.push({
                        thumbnail: currentArticle.thumbnail,
                        title: currentArticle.title,
                        link: currentArticle.link,
                        pubDate: currentArticle.pubDate,
                    });

                    // Thêm bài viết này vào Set để tránh trùng lặp
                    existingTitles.add(currentArticle.title);
                    existingThumbnails.add(currentArticle.thumbnail);
                    existingLinks.add(currentArticle.link);

                    index++;
                    articleCount++;
                }
            }
        }

        validArticles.sort((a, b) => new Date(b.pubDate) - new Date(a.pubDate))
        validArticles.forEach((validArticle, index) => {
            const missingItemIndex = flashNews.findIndex(el => el.sort === missingItems[index].sort);
            if (missingItemIndex !== -1) {
                flashNews[missingItemIndex].thumbnail = validArticle.thumbnail;
                flashNews[missingItemIndex].title = validArticle.title;
                flashNews[missingItemIndex].link = validArticle.link;
            }
        });

        await redisService.pushKeyToRedis(redisKey, JSON.stringify(flashNews), 900) // 15p

        return flashNews;
    }

    static async updateRss() {
        const rssLinks = await rssLinkRepo.getListRssLinks({
            status: defaultStatus.ACTIVE,
        }, ['link'], [['percent', 'DESC']], null);

        await Promise.all(rssLinks.map(async ({link}) => {
            try {
                const feed = await parser.parseURL(link);

                // Chọn các bài viết và lấy dữ liệu cần thiết
                const articles = feed.items.map(item => {
                    const content = item.content || item.description;
                    const title = item.title;
                    const pubDate = new Date(item.pubDate);

                    const thumbnailMatch = content.match(/<img[^>]+src="([^">]+)"/);
                    const linkMatch = content.match(/<a href=['"]([^'"]+)['"]/);
                    const thumbnail = thumbnailMatch ? thumbnailMatch[1] : item.enclosure?.url;
                    const articleLink = linkMatch ? linkMatch[1] : item.link;

                    return {title, pubDate, link: articleLink, thumbnail};
                });

                const redisKey = `cache:rss:${link}`;
                const expireTime = 24 * 60 * 60; // 24 giờ (86,400 giây)

                // Sử dụng redisService để lưu articles
                await redisService.pushKeyToRedis(redisKey, JSON.stringify(articles), expireTime);
            } catch (err) {
                sendMessage(`*[CẢNH BÁO]* [[FUS APP]]\nLấy dữ liệu từ link RSS "${link}" thất bại.`)
            }
        }));

        // Xoá cache
        const newsKey = `cache:news:*`;
        const flashNewsKey = `cache:flash_news`;
        await redisService.deleteKeysByPattern(newsKey);
        await redisService.deleteKeyFromRedis(flashNewsKey);
    }
}

module.exports = NewsService;