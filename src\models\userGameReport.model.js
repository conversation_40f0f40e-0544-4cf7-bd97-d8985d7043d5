"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const userGameReportModel = sequelize.define('user_game_report', {
    fid: {
        type: DataTypes.STRING,
        allowNull: false,
        primaryKey: true,
    },
    uid: {
        type: DataTypes.STRING,
        allowNull: true,
        primaryKey: true,
    },
    games: {
        type: DataTypes.JSON,
        allowNull: false,
    },
}, {
    timestamps: false,
    underscored: true,
    indexes: [
        {
            fields: ['fid', 'uid'],
        },
    ],
    tableName: 'user_game_report'
})

module.exports = userGameReportModel