"use strict";

const {OK} = require("../../../utils/successResponse");
const h5GameService = require("../../../services/website/v1/h5Game.service");

class H5GameController {
    getListH5Games = async (req, res, next) => {
        new OK({
            message: 'Get list h5 games successfully.',
            metadata: await h5GameService.getListH5Games(req)
        }).send(res)
    }

    getListH5GameCategories = async (req, res, next) => {
        new OK({
            message: 'Get list h5 game categories successfully.',
            metadata: await h5GameService.getListH5GameCategories(req)
        }).send(res)
    }

    getListTrendingH5Games = async (req, res, next) => {
        new OK({
            message: 'Get list trending h5 games successfully.',
            metadata: await h5GameService.getListTrendingH5Games(req)
        }).send(res)
    }

    getListUserH5Games = async (req, res, next) => {
        new OK({
            message: 'Get list user h5 games successfully.',
            metadata: await h5GameService.getListUserH5Games(req)
        }).send(res)
    }
}

module.exports = new H5GameController();