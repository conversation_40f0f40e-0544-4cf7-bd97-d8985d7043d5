"use strict";

const crypto = require("node:crypto");
const {BadRequestError, ForbiddenError} = require("../../utils/errorResponse");
const HEADERS = {
    X_TIMESTAMP: 'x-timestamp',
    X_SIGNATURE: 'x-signature',
}

async function verifyClient(req, res, next) {
    const timestamp = req.headers[HEADERS.X_TIMESTAMP]?.toString();
    const clientSignature = req.headers[HEADERS.X_SIGNATURE]?.toString();

    if (!timestamp || !clientSignature) {
        return next(new BadRequestError(null, "Missing timestamp or signature"));
    }

    const currentTimestamp = Date.now();
    const MAX_TIME_DIFF = 5 * 60 * 1000; // 5 phút

    // Kiểm tra timestamp không quá cũ
    if (Math.abs(currentTimestamp - timestamp) > MAX_TIME_DIFF) {
        return next(new BadRequestError(null, "Request timestamp is invalid or expired"));
    }

    // Tạo lại signature từ dữ liệu nhận được
    const payload = JSON.stringify(req.body || ""); // Body của request
    const serverSignature = generateSignature(process.env.SIGNATURE_SECRET_KEY, timestamp, payload);

    // So sánh signature
    const clientSignatureBuffer = Buffer.from(clientSignature);
    const serverSignatureBuffer = Buffer.from(serverSignature);
    if (clientSignatureBuffer.length !== serverSignatureBuffer.length || !crypto.timingSafeEqual(clientSignatureBuffer, serverSignatureBuffer)) {
        return next(new ForbiddenError("Invalid signature"));
    }

    next();
}

async function verifyClientV2(req, res, next) {
    const timestamp = Number(req.headers[HEADERS.X_TIMESTAMP]);
    const clientSignature = req.headers[HEADERS.X_SIGNATURE]?.toString();

    if (!timestamp || isNaN(timestamp) || !clientSignature) {
        return next(new BadRequestError(null, "Missing timestamp or signature"));
    }

    const currentTimestamp = Date.now();
    const MAX_TIME_DIFF = 5 * 60 * 1000; // 5 phút

    // Kiểm tra timestamp không quá cũ
    if (Math.abs(currentTimestamp - timestamp) > MAX_TIME_DIFF) {
        return next(new BadRequestError(null, "Request timestamp is invalid or expired"));
    }

    // Tạo lại signature từ dữ liệu nhận được
    const payload = req.method === 'GET' ? {
        ...req.query,
        timestamp
    } : {
        ...req.body,
        timestamp
    }
    const serverSignature = generateSignature(process.env.SIGNATURE_SECRET_KEY, timestamp, JSON.stringify(payload));

    // So sánh signature
    const clientSignatureBuffer = Buffer.from(clientSignature);
    const serverSignatureBuffer = Buffer.from(serverSignature);
    if (clientSignatureBuffer.length !== serverSignatureBuffer.length || !crypto.timingSafeEqual(clientSignatureBuffer, serverSignatureBuffer)) {
        return next(new ForbiddenError("Invalid signature"));
    }

    next();
}

const generateSignature = (secretKey, timestamp, payload = "") => {
    const dataToSign = timestamp + payload;
    return crypto.createHmac("sha256", secretKey).update(dataToSign).digest("hex");
}

module.exports = {
    verifyClient,
    verifyClientV2,
    generateSignature
}