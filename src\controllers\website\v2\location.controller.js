"use strict";

const {OK} = require("../../../utils/successResponse");
const locationService = require("../../../services/website/v1/location.service");

class LocationController {
    getListLocations = async (req, res, next) => {
        new OK({
            message: 'Get list locations successfully.',
            metadata: await locationService.getListLocations(req)
        }).send(res)
    }

    getLocationDetail = async (req, res, next) => {
        new OK({
            message: 'Get location detail successfully.',
            metadata: await locationService.getLocationDetail(req)
        }).send(res)
    }
}

module.exports = new LocationController();