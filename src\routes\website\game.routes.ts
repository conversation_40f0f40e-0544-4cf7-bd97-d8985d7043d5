import { Router } from 'express';
import { asyncHandler } from '@/utils/asyncHandler';
import { validateBody, validateQuery, validateParams } from '@/middlewares/validation.middleware';
import {
  gameQuerySchema,
  createGameSchema,
  openGameSchema,
  gameDetailParamsSchema,
  topGamesParamsSchema,
  rssRequestSchema,
} from '@/schemas/game.schema';

// Import controllers (will be dynamically loaded based on API version)
const controllerName = 'game';

const router = Router();

/**
 * Game Routes
 */

// GET /game - Get list of games
router.get(
  '/',
  validateQuery(gameQuerySchema),
  asyncHandler(async (req, res, next) => {
    const { asyncVersionHandler } = await import('@/utils/asyncHandler');
    return asyncVersionHandler(controllerName, 'getListGames')(req, res, next);
  })
);

// POST /game - Create new game
router.post(
  '/',
  validateBody(createGameSchema),
  asyncHandler(async (req, res, next) => {
    const { asyncVersionHandler } = await import('@/utils/asyncHandler');
    return asyncVersionHandler(controllerName, 'createGame')(req, res, next);
  })
);

// GET /game/top/:fid - Get top games by FID
router.get(
  '/top/:fid',
  validateParams(topGamesParamsSchema),
  asyncHandler(async (req, res, next) => {
    const { asyncVersionHandler } = await import('@/utils/asyncHandler');
    return asyncVersionHandler(controllerName, 'getListTopGames')(req, res, next);
  })
);

// GET /game/top/:fid/:uid - Get user games by FID and UID
router.get(
  '/top/:fid/:uid',
  validateParams(topGamesParamsSchema),
  asyncHandler(async (req, res, next) => {
    const { asyncVersionHandler } = await import('@/utils/asyncHandler');
    return asyncVersionHandler(controllerName, 'getListUserGames')(req, res, next);
  })
);

// GET /game/:code - Get game detail by code
router.get(
  '/:code',
  validateParams(gameDetailParamsSchema),
  asyncHandler(async (req, res, next) => {
    const { asyncVersionHandler } = await import('@/utils/asyncHandler');
    return asyncVersionHandler(controllerName, 'getGameDetail')(req, res, next);
  })
);

// POST /game/open - Open game
router.post(
  '/open',
  validateBody(openGameSchema),
  asyncHandler(async (req, res, next) => {
    const { asyncVersionHandler } = await import('@/utils/asyncHandler');
    return asyncVersionHandler(controllerName, 'openGame')(req, res, next);
  })
);

export default router;
