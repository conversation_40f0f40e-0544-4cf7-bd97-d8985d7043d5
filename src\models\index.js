"use strict";

const roleModel = require("./role.model");
const rolePermissionModel = require("./rolePermission.model");
const moduleModel = require("./module.model");
const permissionModel = require("./permission.model");
const adminModel = require("./admin.model");
const adminRoleModel = require("./adminRole.model");
const bannerModel = require("./banner.model");
const bannerV2Model = require("./bannerV2.model");
const bannerTimeSlotModel = require("./bannerTimeSlot.model");
const bannerPositionModel = require("./bannerPosition.model");
const gameModel = require("./game.model");
const selfGameModel = require("./selfGame.model");
const gameReportModel = require("./gameReport.model");
const h5GameModel = require("./h5Game.model");
const h5GameCategoryMatchModel = require("./h5GameCategoryMatch.model");
const h5GameCategoryModel = require("./h5GameCategory.model");
const userH5GameReportModel = require("./userH5GameReport.model");
const locationModel = require("./location.model");
const regionModel = require("./region.model");
const cityModel = require("./city.model");
const videoModel = require("./video.model");
const playlistModel = require("./playlist.model");
const playlistVideoModel = require("./playlistVideo.model");
const bannerReportModel = require("./bannerReport.model");
const clientModel = require("./client.model");
const clientProductModel = require("./clientProduct.model");

// role
roleModel.hasMany(rolePermissionModel, {foreignKey: "role_id"});
rolePermissionModel.belongsTo(roleModel, {foreignKey: "role_id"});

//admin
adminModel.hasMany(adminRoleModel, {foreignKey: "admin_id"});

// admin_role
adminRoleModel.belongsTo(roleModel, {foreignKey: "role_id"});

// module
moduleModel.hasMany(permissionModel, {foreignKey: "module_id"});

// banner
bannerModel.belongsTo(bannerPositionModel, {
    foreignKey: 'position_id'
});
bannerPositionModel.hasMany(bannerModel, {foreignKey: 'position_id'});

// banner v2
bannerV2Model.belongsTo(bannerPositionModel, {foreignKey: 'position_id'});
bannerV2Model.belongsTo(clientModel, {foreignKey: 'client_id'});
bannerV2Model.belongsTo(clientProductModel, {foreignKey: 'client_product_id'});
bannerV2Model.hasMany(bannerTimeSlotModel, {foreignKey: 'banner_id'});
bannerPositionModel.hasMany(bannerV2Model, {foreignKey: 'position_id'});

// game
gameModel.hasMany(gameReportModel, {foreignKey: 'code', sourceKey: 'code'});
gameReportModel.belongsTo(gameModel, {foreignKey: 'code', targetKey: 'code'});
gameReportModel.belongsTo(selfGameModel, {foreignKey: 'code', targetKey: 'code'});
gameReportModel.belongsTo(h5GameModel, {foreignKey: 'code', targetKey: 'code'});

// h5 game
h5GameModel.hasMany(h5GameCategoryMatchModel, {foreignKey: "game_id"});
h5GameCategoryMatchModel.belongsTo(h5GameCategoryModel, {foreignKey: "category_id"});
h5GameModel.hasMany(userH5GameReportModel, {foreignKey: 'code', sourceKey: 'code'});
userH5GameReportModel.belongsTo(h5GameModel, {foreignKey: 'code', targetKey: 'code', as: 'detail'});

// location
locationModel.belongsTo(regionModel, {foreignKey: "region_id"});
locationModel.belongsTo(cityModel, {foreignKey: "city_id"});

// video
videoModel.hasMany(playlistVideoModel, {foreignKey: "video_id"});

// playlist_video
playlistVideoModel.belongsTo(playlistModel, {foreignKey: "playlist_id"});

// link report
bannerReportModel.belongsTo(videoModel, {foreignKey: "banner_id"});
bannerV2Model.hasMany(bannerReportModel, {foreignKey: 'banner_id'});

// client product
clientProductModel.belongsTo(clientModel, {foreignKey: "client_id"});