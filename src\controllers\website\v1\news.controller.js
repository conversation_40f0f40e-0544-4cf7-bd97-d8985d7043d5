"use strict";

const {OK} = require("../../../utils/successResponse");
const newsService = require("../../../services/website/v1/news.service");

class NewsController {
    getListNews = async (req, res, next) => {
        new OK({
            message: 'Get list news successfully.',
            metadata: await newsService.getListNews(req)
        }).send(res)
    }

    getListFlashNews = async (req, res, next) => {
        new OK({
            message: 'Get list flash news successfully.',
            metadata: await newsService.getListFlashNewsV3(req)
        }).send(res)
    }

    updateRss = async (req, res, next) => {
        new OK({
            message: 'Update rss successfully.',
            metadata: await newsService.updateRss(req)
        }).send(res)
    }
}

module.exports = new NewsController();