const buildQueryString = (params) => {
    const queryString = Object.keys(params)
        .filter(key => params[key] !== undefined && params[key] !== null)
        .map(key => `${key}=${params[key]}`)
        .join('&');

    return queryString ? `?${queryString}` : '';
}

const slugify = (text) => {
    return text.normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '').toString().toLowerCase()
        .replace(/đ/g, 'd')
        .replace(/\s+/g, '-')           // Thay khoảng trắng bằng dấu gạch ngang
        .replace(/[^\w-]+/g, '')        // Loại bỏ ký tự không phải chữ, số, gạch ngang
        .replace(/--+/g, '-')           // Loại bỏ nhiều dấu gạch ngang liên tiếp
        .replace(/^-+/, '')             // <PERSON><PERSON><PERSON> bỏ dấu gạch ngang ở đầu chuỗi
        .replace(/-+$/, '');            // Loại bỏ dấu gạch ngang ở cuối chuỗi
}

const getRandomElementInArray = (arr) => arr[Math.floor(Math.random() * arr.length)];

const convertSecondsToTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secondsLeft = seconds % 60;

    let result = `${minutes}:${secondsLeft < 10 ? '0' + secondsLeft : secondsLeft}`;

    if (hours > 0) {
        result = `${hours}:${result}`
    }

    return result;
}

module.exports = {
    buildQueryString,
    slugify,
    getRandomElementInArray,
    convertSecondsToTime
}