"use strict";

const {OK} = require("../../../utils/successResponse");
const settingService = require("../../../services/website/v1/setting.service");

class SettingController {
    getListSettings = async (req, res, next) => {
        new OK({
            message: 'Get list settings successfully.',
            metadata: await settingService.getListSettings(req)
        }).send(res)
    }
}

module.exports = new SettingController();