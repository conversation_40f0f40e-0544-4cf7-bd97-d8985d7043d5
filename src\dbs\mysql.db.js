"use strict";

const {Sequelize} = require('sequelize');
const {MYSQL_DB, MYSQL_USERNAME, MYSQL_PASSWORD, MYSQL_HOST, MYSQL_PORT} = process.env

const sequelize = new Sequelize(MYSQL_DB, MYSQL_USERNAME, MYSQL_PASSWORD, {
    host: MYSQL_HOST,
    port: MYSQL_PORT,
    dialect: 'mysql',
    // timezone: '+07:00',
    dialectOptions: {
        dateStrings: true,
        typeCast: true,
        connectTimeout: 30000, // Tăng timeout kết nối
    },
    logging: process.env.NODE_ENV === 'production' ? false : console.log,
    pool: {
        max: 50,        // Tăng số lượng kết nối tối đa
        min: 10,         // Giữ ít nhất 10 kết nối
        acquire: 60000, // Timeout khi lấy kết nối (ms)
        idle: 30000     // Thời gian tối đa gi<PERSON> kết nối không sử dụng (ms)
    }
})

// DB logs
const logDB = new Sequelize("logs", MYSQL_USERNAME, MYSQL_PASSWORD, {
    host: MYSQL_HOST,
    port: MYSQL_PORT,
    dialect: 'mysql',
    // timezone: '+07:00',
    dialectOptions: {
        dateStrings: true,
        typeCast: true,
        connectTimeout: 30000, // Tăng timeout kết nối
    },
    logging: process.env.NODE_ENV === 'production' ? false : console.log,
    pool: {
        max: 20,        // Tăng số lượng kết nối tối đa
        min: 5,         // Giữ ít nhất 5 kết nối
        acquire: 60000, // Timeout khi lấy kết nối (ms)
        idle: 30000     // Thời gian tối đa giữ kết nối không sử dụng (ms)
    }
});

// const connectDatabase = async () => {
//     try {
//         await sequelize.authenticate();
//         console.log('Database connected.');
//     } catch (error) {
//         console.error('Unable to connect to the database:', error);
//         throw error;
//     }
// };

const connectDatabase = async (retries = 5) => {
    while (retries) {
        try {
            await sequelize.authenticate();
            console.log('✅ Database connected.');
            return;
        } catch (error) {
            console.error(`❌ Unable to connect to database. Retries left: ${retries}`, error);
            retries -= 1;
            await new Promise(res => setTimeout(res, 5000)); // Chờ 5s trước khi thử lại
        }
    }
    throw new Error("❌ Database connection failed after multiple retries.");
};

const connectLogDatabase = async (retries = 5) => {
    while (retries) {
        try {
            await logDB.authenticate()
            console.log('✅ Database logs connected.');
            return;
        } catch (error) {
            console.error(`❌ Unable to connect to database logs. Retries left: ${retries}`, error);
            retries -= 1;
            await new Promise(res => setTimeout(res, 5000)); // Chờ 5s trước khi thử lại
        }
    }
    throw new Error("❌ Database logs connection failed after multiple retries.");
};

module.exports = {
    sequelize,
    logDB,
    connectDatabase,
    connectLogDatabase
};