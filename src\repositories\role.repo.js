"use strict";

const roleModel = require("../models/role.model");

class RoleRepo {
    static async getListRoles(filter, include = null, attributes, order, limit) {
        return await roleModel.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            order: order
        });
    }

    static async getListRolesPagination(filter, include = null, attributes = null, order, page, limit) {
        const {count, rows} = await roleModel.findAndCountAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            offset: (page - 1) * limit,
            order: order
        });
        return {
            total_items: count,
            items_per_page: limit,
            total_pages: Math.ceil(count / limit),
            current_page: page,
            items: rows
        };
    }

    static async getRoleDetail(filter, include = null, attributes = []) {
        return await roleModel.findOne({
            where: filter,
            include: include,
            attributes: attributes
        })
    }

    static async createRole(data) {
        const result = await roleModel.create(data);
        return result.id;
    }

    static async updateRole(model, data) {
        await model.update(data);
    }
}

module.exports = RoleRepo;