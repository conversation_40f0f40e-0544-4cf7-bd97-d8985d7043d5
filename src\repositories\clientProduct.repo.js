"use strict";

const clientProductModel = require("../models/clientProduct.model");

class clientProductRepo {
    static async getListClientProducts(filter, include, attributes, order, limit) {
        return await clientProductModel.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            order: order
        });
    }

    static async getListClientProductsPagination(filter, include, attributes, order, page, limit) {
        const {count, rows} = await clientProductModel.findAndCountAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            offset: (page - 1) * limit,
            order: order,
            distinct: true
        });

        return {
            total_items: count,
            items_per_page: limit,
            total_pages: Math.ceil(count / limit),
            current_page: page,
            items: rows
        };
    }

    static async getClientProductDetail(filter, include = null, attributes = []) {
        return await clientProductModel.findOne({
            where: filter,
            include: include,
            attributes: attributes
        })
    }

    static async createClientProduct(data) {
        const result = await clientProductModel.create(data);
        return result.id;
    }

    static async updateClientProduct(model, data) {
        await model.update(data);
    }
}

module.exports = clientProductRepo;