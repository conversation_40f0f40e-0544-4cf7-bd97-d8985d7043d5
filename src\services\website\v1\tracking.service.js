"use strict";

const moment = require("moment-timezone");
const {updateLinkReportQueue} = require("../../../queues/updateLinkReport.queue");
const linkReportRepo = require("../../../repositories/linkReport.repo");
const bannerReportRepo = require("../../../repositories/bannerReport.repo");
const {updateBannerReportQueue} = require("../../../queues/updateBannerReport.queue");
const {literal} = require("sequelize");
const {itemReportTypes} = require("../../../enums");
const redisService = require("../../redis.service");
const eventRepo = require("../../../repositories/event.repo");

class TrackingService {
    static async openLink(req) {
        const {link, type, fid} = req.body;
        const currentMonth = moment().tz("Asia/Ho_Chi_Minh").month() + 1;
        const currentYear = moment().tz("Asia/Ho_Chi_Minh").year();

        if (link && type) {
            updateLinkReportQueue.add({
                link,
                type,
                fid: fid || 0,
                month: currentMonth,
                year: currentYear
            }, {
                removeOnComplete: true,
            });
        }
    }

    static async openBanner(req) {
        const {banner_id, fid} = req.body;
        const currentDate = moment().tz('Asia/Ho_Chi_Minh').format('YYYY-MM-DD');

        if (banner_id) {
            updateBannerReportQueue.add({
                item_id: banner_id,
                fid: fid || 0,
                type: itemReportTypes.BANNER_CLICK,
                currentDate
            }, {
                removeOnComplete: true,
            });
        }
    }

    static async openVideo(req) {
        const {video_id, fid} = req.body;
        const currentDate = moment().tz('Asia/Ho_Chi_Minh').format('YYYY-MM-DD');

        if (video_id) {
            updateBannerReportQueue.add({
                item_id: video_id,
                fid: fid || 0,
                type: itemReportTypes.VIDEO,
                currentDate
            }, {
                removeOnComplete: true,
            });
        }
    }

    static async updateLinkReport(link, type, fid, month, year) {
        const existLinkReport = await linkReportRepo.getLinkReportDetail({
                link,
                type,
                fid,
                month,
                year
            }, ['link', 'type', 'fid', 'month', 'year', 'total_open']
        );

        if (existLinkReport) {
            await linkReportRepo.updateLinkReport(existLinkReport, {
                total_open: existLinkReport.total_open + 1,
            });
        } else {
            await linkReportRepo.createLinkReport({
                link,
                type,
                fid,
                month,
                year,
                total_open: 1,
            });
        }
    }

    static async updateBannerReport(itemId, fid, type, currentDate) {
        const month = moment.tz(currentDate, 'YYYY-MM-DD', 'Asia/Ho_Chi_Minh').month() + 1;
        const year = moment.tz(currentDate, 'YYYY-MM-DD', 'Asia/Ho_Chi_Minh').year();

        const existBannerReport = await bannerReportRepo.getBannerReportDetail({
                banner_id: itemId,
                fid,
                type,
                month,
                year
            }, ['banner_id', 'fid', 'type', 'month', 'year', 'total_open', [
                literal(`CAST(banner_report.data AS JSON)`),
                'data'
            ]]
        );

        if (existBannerReport) {
            let dayData = existBannerReport.data
            const index = dayData.findIndex(item => item.day === currentDate);

            if (index !== -1) {
                dayData[index].total_open += 1;
            } else {
                dayData.unshift({day: currentDate, total_open: 1});
            }

            await bannerReportRepo.updateBannerReport(existBannerReport, {
                data: JSON.stringify(dayData),
                total_open: existBannerReport.total_open + 1,
            });
        } else {
            const dayData = [{
                day: currentDate,
                total_open: 1
            }]

            await bannerReportRepo.createBannerReport({
                banner_id: itemId,
                fid,
                type,
                month,
                year,
                data: JSON.stringify(dayData),
                total_open: 1,
            });
        }

        // Xoá cache
        if (type === itemReportTypes.VIDEO) {
            const key = `cache:top_view_videos:${fid}`
            await redisService.deleteKeyFromRedis(key);
        }
    }

    static async logEvent(fid, uid, pcn, version) {
        const newEvent = {
            fid,
            uid,
            pcname: pcn,
            version
        }
        await eventRepo.createEvent(newEvent)
    }
}

module.exports = TrackingService;