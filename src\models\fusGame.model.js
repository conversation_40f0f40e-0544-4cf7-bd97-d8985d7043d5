"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const fusGameModel = sequelize.define('fus_games', {
    id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
    },
    fid: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    data: {
        type: DataTypes.JSON,
        allowNull: false,
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: true,
    },
    created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: true,
    },
    updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
}, {
    timestamps: false,
    underscored: true,
    defaultScope: {
        attributes: {exclude: ['created_by', 'updated_at', 'updated_by']}
    },
    tableName: 'fus_games'
})

module.exports = fusGameModel