"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const rolePermissionModel = sequelize.define('role_permission', {
    role_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    permission_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
}, {
    tableName: 'role_permission', // Tên bảng của bạn
    timestamps: false, // Tắt tự động tạo createdAt và updatedAt
    underscored: true, // Sử dụng kiểu snake_case cho tên cột
    freezeTableName: true, // <PERSON><PERSON><PERSON> nguyên tên bảng không thay đổi
    id: false, // Tắt trường id
});

module.exports = rolePermissionModel;