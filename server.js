"use strict";

const app = require("./src/app");
const {connectDatabase, connectLogDatabase} = require("./src/dbs/mysql.db");
const {connectRedis} = require("./src/dbs/ioredis.db");

const startServer = async () => {
    try {
        await connectDatabase();
        await connectLogDatabase();
        await connectRedis();

        app.listen(process.env.APP_PORT, () => {
            console.log(`App server listening on port: ${process.env.APP_PORT || 3004}`);
        });
    } catch (err) {
        console.error('App server error:', err.stack);
    }
};

startServer();