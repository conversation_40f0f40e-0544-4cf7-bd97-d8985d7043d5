"use strict";

const moment = require("moment-timezone");
const {Op, literal} = require("sequelize");
const bannerRepo = require("../../../repositories/banner.repo");
const bannerV2Repo = require("../../../repositories/bannerV2.repo");
const bannerPositionRepo = require("../../../repositories/bannerPosition.repo");
const locationRepo = require("../../../repositories/location.repo");
const bannerTimeSlotModel = require("../../../models/bannerTimeSlot.model");
const clientModel = require("../../../models/client.model");
const clientProductModel = require("../../../models/clientProduct.model");
const {defaultStatus, bannerV2Status, adminStatus} = require("../../../enums/objStatus");
const {
    bannerV2LocationTypes,
    bannerPositionTypes,
    bannerV2DisplayTypes,
    bannerV2DestinationTypes, itemReportTypes
} = require("../../../enums");
const {getRandomElementInArray} = require("../../../utils");
const redisService = require("../../redis.service");
const {updateBannerReportQueue} = require("../../../queues/updateBannerReport.queue");

class BannerService {
    static async getListBanners(req) {
        const {fid, position, limit} = req.query;
        let result = null

        if (!fid) return null

        const bannerPosition = await bannerPositionRepo.getBannerPositionDetail({
                id: position,
                status: defaultStatus.ACTIVE,
                deleted_at: null,
                deleted_by: null
            },
            null,
            ['type']);
        if (!bannerPosition) {
            // throw new NotFoundError('Vị trí lấy banner không tồn tại!')
            return null
        }

        if (bannerPosition.type === 1) {
            result = await bannerRepo.getListBanners({
                    fid,
                    position_id: position,
                    status: defaultStatus.ACTIVE,
                    deleted_at: null,
                    deleted_by: null
                },
                null,
                ['name', 'image', 'link'],
                [['rank', 'ASC'], ['created_at', 'DESC']],
                limit ? parseInt(limit) : 1
            )

            if (!result || result.length === 0) {
                result = await bannerRepo.getListBanners({
                        fid: 0,
                        position_id: position,
                        status: defaultStatus.ACTIVE,
                        deleted_at: null,
                        deleted_by: null
                    },
                    null,
                    ['name', 'image', 'link'],
                    [['rank', 'ASC'], ['created_at', 'DESC']],
                    limit ? parseInt(limit) : 1
                )
            }
        } else {
            result = await bannerRepo.getListBanners({
                    fid,
                    position_id: position,
                    status: defaultStatus.ACTIVE,
                    deleted_at: null,
                    deleted_by: null
                },
                null,
                ['name', 'image', 'link', 'ratio'],
                [['ratio', 'DESC'], ['created_at', 'DESC']],
                limit ? parseInt(limit) : null
            )

            if (!result || result.length === 0) {
                result = await bannerRepo.getListBanners({
                        fid: 0,
                        position_id: position,
                        status: defaultStatus.ACTIVE,
                        deleted_at: null,
                        deleted_by: null
                    },
                    null,
                    ['name', 'image', 'link', 'ratio'],
                    [['ratio', 'DESC'], ['created_at', 'DESC']],
                    limit ? parseInt(limit) : null
                )
            }
        }

        return result
    }

    static async getListBannersV2(req) {
        const {fid, position, limit} = req.query;

        return this.getBannerV2ByPosition(fid, position, limit)
    }

    static async getBannerV2ByPosition(fid, position, limit) {
        const now = moment().tz("Asia/Ho_Chi_Minh");
        const currentDate = now.format("YYYY-MM-DD");
        const currentTime = now.format("HH:mm:ss");
        let result = null

        if (!fid) return null

        const redisKey = `cache:banners:${fid}:${position}:${limit}`

        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            result = cachedData === "{}" ? null : JSON.parse(cachedData);

            if (Array.isArray(result) && result.length) {
                await Promise.all(result.map((item) => {
                    if (item?.id) {
                        updateBannerReportQueue.add({
                            item_id: item.id,
                            fid: fid,
                            type: itemReportTypes.BANNER_SHOW,
                            currentDate
                        }, {
                            removeOnComplete: true,
                        });
                    }
                }));
            } else {
                if (result?.id) {
                    updateBannerReportQueue.add({
                        item_id: result.id,
                        fid: fid,
                        type: itemReportTypes.BANNER_SHOW,
                        currentDate
                    }, {
                        removeOnComplete: true,
                    });
                }
            }

            return result
        }

        const bannerPosition = await bannerPositionRepo.getBannerPositionDetail({
                id: position,
                status: defaultStatus.ACTIVE,
                deleted_at: null,
                deleted_by: null
            },
            null,
            ['type']);
        if (!bannerPosition) {
            // throw new NotFoundError('Vị trí lấy banner không tồn tại!')
            return null
        }

        const location = await locationRepo.getLocationDetail({
                fid: fid,
                status: defaultStatus.ACTIVE,
            },
            null,
            ['fid', 'region_id', 'city_id', 'type']);
        if (!location) {
            // throw new NotFoundError('Cơ sở không tồn tại!')
            return null
        }

        let bannerConditions = {
            apply_to: literal(`JSON_CONTAINS(banners_v2.apply_to, CAST('"${location.type}"' AS JSON))`),
            position_id: position,
            status: bannerV2Status.APPROVED,
            deleted_at: null,
            deleted_by: null,
            [Op.and]: []
        };

        if (fid) {
            const orConditions = [];

            if (location.region_id) {
                const val = JSON.stringify(location.region_id.toString());
                orConditions.push({
                    location_type: bannerV2LocationTypes.REGION,
                    location_value: literal(`JSON_CONTAINS(location_value, '${val}')`),
                    region: literal(`JSON_CONTAINS(region, '${val}')`)
                });
            }

            if (location.city_id) {
                const val = JSON.stringify(location.city_id.toString());
                orConditions.push({
                    location_type: bannerV2LocationTypes.CITY,
                    location_value: literal(`JSON_CONTAINS(location_value, '${val}')`),
                    city: literal(`JSON_CONTAINS(city, '${val}')`)
                });
            }

            if (location.fid) {
                const val = JSON.stringify(location.fid.toString());
                orConditions.push({
                    location_type: bannerV2LocationTypes.FID,
                    location_value: literal(`JSON_CONTAINS(location_value, '${val}')`),
                    fid: literal(`JSON_CONTAINS(fid, '${val}')`)
                });
            }

            if (orConditions.length > 0) {
                bannerConditions[Op.and].push({ [Op.or]: orConditions });
            }
        }

        if (bannerPosition.type === bannerPositionTypes.BANNER) {
            bannerConditions[Op.and].push({
                [Op.or]: [
                    {
                        apply_start_date: {[Op.lte]: currentDate},
                        apply_end_date: {[Op.gte]: currentDate}
                    },
                    {
                        apply_start_date: {[Op.is]: null},
                        apply_end_date: {[Op.is]: null}
                    }
                ]
            });
        } else {
            bannerConditions[Op.and].push({
                apply_start_date: {[Op.lte]: currentDate},
                apply_end_date: {[Op.gte]: currentDate}
            });
        }

        const activeBanners = await bannerV2Repo.getListBanners(
            bannerConditions,
            [
                {
                    model: clientModel,
                    required: true,
                    where: {
                        status: adminStatus.ACTIVE,
                        deleted_at: null,
                        deleted_by: null,
                    },
                    attributes: ['id']
                },
                {
                    model: clientProductModel,
                    required: true,
                    where: {
                        status: defaultStatus.ACTIVE,
                        deleted_at: null,
                        deleted_by: null,
                    },
                    attributes: ['id']
                },
                {
                    model: bannerTimeSlotModel,
                    required: false,
                    where: {
                        start_time: {[Op.lte]: currentTime},
                        end_time: {[Op.gt]: currentTime}
                    },
                    attributes: ['start_time', 'end_time']
                }
            ],
            ["id", "image", "apply_date", "display_type", "destination_type", "active_click", "sort", [
                literal(`CAST(banners_v2.redirect_link AS JSON)`),
                'redirect_link'
            ]],
            [
                [literal('COALESCE(sort, 51)'), 'ASC'],
                ['id', 'ASC']
            ],
            [bannerPositionTypes.POPUP, bannerPositionTypes.ICON_GAME_HOT].includes(bannerPosition.type) ? null : (limit ? parseInt(limit) : 1)
        )

        let expireTime = 60 // 1p

        if (bannerPosition.type === bannerPositionTypes.BANNER) {
            const banners = activeBanners.filter(item => (!item.apply_date || (item.apply_date && item.banner_time_slots.length)))
            result = banners.map(item => {
                return {
                    id: item?.id,
                    image: item?.image,
                    redirect_link: item?.destination_type === bannerV2DestinationTypes.LAUNCHER && typeof item?.redirect_link === "string" ? {code: item?.redirect_link} : item?.redirect_link,
                }
            })

            await Promise.all(result.map((item) => {
                if (item?.id) {
                    updateBannerReportQueue.add({
                        item_id: item.id,
                        fid: fid,
                        type: itemReportTypes.BANNER_SHOW,
                        currentDate
                    }, {
                        removeOnComplete: true,
                    });
                }
            }));
        } else {
            const exclusiveBanners = activeBanners.filter(item => item.display_type === bannerV2DisplayTypes.EXCLUSIVE && item.banner_time_slots.length).sort((a, b) => a.id - b.id)
            const randomSharingBanners = activeBanners.filter(item => item.display_type === bannerV2DisplayTypes.RANDOM_SHARING && item.banner_time_slots.length)

            if (exclusiveBanners.length) {
                result = {
                    id: exclusiveBanners[0]?.id,
                    image: exclusiveBanners[0]?.image,
                    destination_type: exclusiveBanners[0]?.destination_type,
                    active_click: exclusiveBanners[0]?.active_click,
                    redirect_link: exclusiveBanners[0]?.destination_type === bannerV2DestinationTypes.LAUNCHER && typeof exclusiveBanners[0]?.redirect_link === "string" ? {code: exclusiveBanners[0]?.redirect_link} : exclusiveBanners[0]?.redirect_link,
                }
            } else if (randomSharingBanners.length) {
                const randomBanner = getRandomElementInArray(randomSharingBanners)

                result = {
                    id: randomBanner?.id,
                    image: randomBanner?.image,
                    destination_type: randomBanner?.destination_type,
                    active_click: randomBanner?.active_click,
                    redirect_link: randomBanner?.destination_type === bannerV2DestinationTypes.LAUNCHER && typeof randomBanner?.redirect_link === "string" ? {code: randomBanner?.redirect_link} : randomBanner?.redirect_link,
                }

                expireTime = 0
            }

            if (result?.id) {
                updateBannerReportQueue.add({
                    item_id: result.id,
                    fid: fid,
                    type: itemReportTypes.BANNER_SHOW,
                    currentDate
                }, {
                    removeOnComplete: true,
                });
            }
        }

        // Nếu là BANNER thì mới cache
        if (expireTime > 0) {
            await redisService.pushKeyToRedis(redisKey, JSON.stringify(result), expireTime)
        }

        return result
    }
}

module.exports = BannerService;