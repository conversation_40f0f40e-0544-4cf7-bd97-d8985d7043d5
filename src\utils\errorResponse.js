"use strict";

const {statusCodes, reasonPhrases} = require("../enums/index")

class ErrorResponse extends <PERSON>rror {
    constructor(message, code, errors = null) {
        super(message)
        this.status = code
        this.errors = errors
    }
}

class InternalServerError extends ErrorResponse {
    constructor(message = reasonPhrases.INTERNAL_SERVER_ERROR, code = statusCodes.INTERNAL_SERVER_ERROR) {
        super(message, code)
    }
}

class ConflictRequestError extends ErrorResponse {
    constructor(message = reasonPhrases.CONFLICT, code = statusCodes.CONFLICT) {
        super(message, code)
    }
}

class BadRequestError extends ErrorResponse {
    constructor(errors = null, message = reasonPhrases.BAD_REQUEST, code = statusCodes.BAD_REQUEST) {
        super(message, code, errors)
    }
}

class AuthFailureError extends ErrorResponse {
    constructor(errors = null, message = reasonPhrases.UNAUTHORIZED, code = statusCodes.UNAUTHORIZED) {
        super(message, code, errors)
    }
}

class NotFoundError extends ErrorResponse {
    constructor(errors = null, message = reasonPhrases.NOT_FOUND, code = statusCodes.NOT_FOUND) {
        super(message, code, errors)
    }
}

class ForbiddenError extends ErrorResponse {
    constructor(message = reasonPhrases.FORBIDDEN, code = statusCodes.FORBIDDEN) {
        super(message, code)
    }
}

class TooManyRequestError extends ErrorResponse {
    constructor(message = reasonPhrases.TOO_MANY_REQUESTS, code = statusCodes.TOO_MANY_REQUESTS) {
        super(message, code)
    }
}

module.exports = {
    InternalServerError,
    ConflictRequestError,
    BadRequestError,
    AuthFailureError,
    NotFoundError,
    ForbiddenError,
    TooManyRequestError
}