"use strict";

const playlistRepo = require("../../../repositories/playlist.repo");
const {defaultStatus, videoStatus} = require("../../../enums/objStatus");
const {literal, Op} = require("sequelize");
const redisService = require("../../redis.service");

class PlaylistService {
    static async getListPlaylist(req) {
        const redisKey = `cache:playlist`

        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        const result = await playlistRepo.getListPlaylists({
                status: defaultStatus.ACTIVE,
                [Op.and]: literal(`(
                    SELECT COUNT(*)
                    FROM playlist_video AS pv
                    INNER JOIN videos AS v ON pv.video_id = v.id
                    WHERE pv.playlist_id = playlist.id
                    AND v.status = ${videoStatus.ACTIVE}
                ) > 0`),
                deleted_at: null,
                deleted_by: null
            },
            null,
            ['name', 'slug', 'icon'],
            [
                [literal('(CASE WHEN sort IS NULL THEN 1 ELSE 0 END)'), 'ASC'],
                ['sort', 'ASC'],
                ['created_at', 'DESC']
            ],
            null
        );

        await redisService.pushKeyToRedis(redisKey, JSON.stringify(result), 86400) // 1 ngày

        return result
    }
}

module.exports = PlaylistService;