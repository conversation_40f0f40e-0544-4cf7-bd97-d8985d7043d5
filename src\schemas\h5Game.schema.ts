import { z } from 'zod';
import { baseQuerySchema, fidUidParamsSchema } from './common.schema';

// H5 Game query parameters schema
export const h5GameQuerySchema = baseQuerySchema.extend({
  fid: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined),
  uid: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined),
  category_id: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined),
  is_trending: z.string().optional().transform(val => val === 'true'),
});

// H5 Game user params schema (for /:fid/:uid endpoint)
export const h5GameUserParamsSchema = fidUidParamsSchema;

// H5 Game response schema
export const h5GameResponseSchema = z.object({
  id: z.number(),
  name: z.string(),
  slug: z.string(),
  description: z.string().nullable(),
  thumbnail: z.string().nullable(),
  game_url: z.string(),
  is_trending: z.boolean(),
  play_count: z.number(),
  rating: z.number(),
  status: z.number(),
  created_at: z.date(),
  updated_at: z.date(),
});

// H5 Game category response schema
export const h5GameCategoryResponseSchema = z.object({
  id: z.number(),
  name: z.string(),
  slug: z.string(),
  icon: z.string().nullable(),
  sort_order: z.number(),
  status: z.number(),
  created_at: z.date(),
  updated_at: z.date(),
});

// H5 Game list response schema
export const h5GameListResponseSchema = z.object({
  games: z.array(h5GameResponseSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
});

// H5 Game categories list response schema
export const h5GameCategoriesResponseSchema = z.object({
  categories: z.array(h5GameCategoryResponseSchema),
  total: z.number(),
});

// Type exports
export type H5GameQueryParams = z.infer<typeof h5GameQuerySchema>;
export type H5GameUserParams = z.infer<typeof h5GameUserParamsSchema>;
export type H5GameResponse = z.infer<typeof h5GameResponseSchema>;
export type H5GameCategoryResponse = z.infer<typeof h5GameCategoryResponseSchema>;
export type H5GameListResponse = z.infer<typeof h5GameListResponseSchema>;
export type H5GameCategoriesResponse = z.infer<typeof h5GameCategoriesResponseSchema>;
