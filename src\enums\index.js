"use strict";

const oegSites = {
    1: 'Chung',
    2: 'Game menu',
    3: 'News',
    4: 'Stadium',
    5: 'Báo cáo',
}

const bannerV2DisplayTypes = {
    EXCLUSIVE: 1,
    RANDOM_SHARING: 2,
}

const bannerV2DestinationTypes = {
    URL: 1,
    LAUNCHER: 2,
}

const bannerV2LocationTypes = {
    REGION: 1,
    CITY: 2,
    FID: 3,
}

const bannerPositionTypes = {
    BANNER: 1,
    POPUP: 2,
    ICON_GAME_HOT: 3,
}

const videoTypes = {
    YOUTUBE: 1
}

const linkReportTypes = {
    NEWS: 1,
    VIDEO: 2,
    BACK_TO_OLD_VERSION: 3,
}

const itemReportTypes = {
    BANNER_CLICK: 1,
    VIDEO: 2,
    BANNER_SHOW: 3,
}

const locationTypes = {
    OEG: 1,
    OTHER: 2,
}

const OEGDefaultSettings = [
    {
        "name": "Facebook icon",
        "key": "fb_icon",
        "value": "https://statics.oeg.vn/storage/FUS_APP_IMAGES/logo_facebook.webp"
    },
    {
        "name": "Facebook link",
        "key": "fb_link",
        "value": "https://www.facebook.com/"
    },
    {
        "name": "Tiêu đề trang chủ",
        "key": "home_page_title",
        "value": "Experience Gaming Heaven"
    },
    {
        "name": "Instagram icon",
        "key": "ins_icon",
        "value": "https://statics.oeg.vn/storage/FUS_APP_IMAGES/logo_instagram.webp"
    },
    {
        "name": "Instagram link",
        "key": "ins_link",
        "value": "https://www.instagram.com/"
    },
    {
        "name": "Logo",
        "key": "logo",
        "value": ""
    },
    {
        "name": "Tiktok icon",
        "key": "tiktok_icon",
        "value": "https://statics.oeg.vn/storage/FUS_APP_IMAGES/logo_tiktok.webp"
    },
    {
        "name": "Tiktok link",
        "key": "tiktok_link",
        "value": "https://www.tiktok.com/"
    },
    {
        "name": "Tổng số bài viết",
        "key": "total_news",
        "value": "30"
    },
    {
        "name": "Website icon",
        "key": "website_icon",
        "value": "https://statics.oeg.vn/storage/FUS_APP_IMAGES/logo_global.webp"
    },
    {
        "name": "Website link",
        "key": "website_link",
        "value": "https://oeg.vn/"
    },
    {
        "name": "App sound",
        "key": "app_sound",
        "value": true
    },
    {
        "name": "Go to game menu",
        "key": "go_to_game_menu",
        "value": false
    }
]

const OtherDefaultSettings = [
    {
        "name": "Facebook icon",
        "key": "fb_icon",
        "value": null
    },
    {
        "name": "Facebook link",
        "key": "fb_link",
        "value": null
    },
    {
        "name": "Tiêu đề trang chủ",
        "key": "home_page_title",
        "value": "For Gamer, by Gamer"
    },
    {
        "name": "Instagram icon",
        "key": "ins_icon",
        "value": null
    },
    {
        "name": "Instagram link",
        "key": "ins_link",
        "value": null
    },
    {
        "name": "Logo",
        "key": "logo",
        "value": ""
    },
    {
        "name": "Tiktok icon",
        "key": "tiktok_icon",
        "value": null
    },
    {
        "name": "Tiktok link",
        "key": "tiktok_link",
        "value": null
    },
    {
        "name": "Tổng số bài viết",
        "key": "total_news",
        "value": "30"
    },
    {
        "name": "Website icon",
        "key": "website_icon",
        "value": null
    },
    {
        "name": "Website link",
        "key": "website_link",
        "value": null
    },
    {
        "name": "App sound",
        "key": "app_sound",
        "value": true
    },
    {
        "name": "Go to game menu",
        "key": "go_to_game_menu",
        "value": false
    }
]

module.exports = {
    oegSites,
    bannerV2DisplayTypes,
    bannerV2DestinationTypes,
    bannerV2LocationTypes,
    bannerPositionTypes,
    videoTypes,
    linkReportTypes,
    itemReportTypes,
    locationTypes,
    OEGDefaultSettings,
    OtherDefaultSettings,
    statusCodes: require('./statusCodes'),
    reasonPhrases: require('./reasonPhrases')
}