import { Response } from 'express';
import { BaseController } from '@/controllers/base.controller';
import { AuthenticatedRequest } from '@/types/common';
import { 
  GameQueryParams, 
  CreateGameRequest, 
  OpenGameRequest,
  GameDetailParams,
  TopGamesParams,
  RssRequest
} from '@/schemas/game.schema';
import { mockDatabase, simulateDbOperation } from '@/data/mockData';
import { Game } from '@/types/api';

export class GameController extends BaseController {
  /**
   * Get list of games
   * GET /game
   */
  public getListGames = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { page, limit, offset } = this.getPaginationParams(req);
      const { sortBy, sortOrder } = this.getSortParams(req);
      const search = this.getSearchParam(req);

      // Simulate database query with filters
      let games = [...mockDatabase.games];

      // Apply search filter
      if (search) {
        games = games.filter(game => 
          game.name.toLowerCase().includes(search.toLowerCase()) ||
          game.description?.toLowerCase().includes(search.toLowerCase())
        );
      }

      // Apply sorting
      games.sort((a, b) => {
        const aValue = (a as any)[sortBy] || '';
        const bValue = (b as any)[sortBy] || '';
        
        if (sortOrder === 'ASC') {
          return aValue > bValue ? 1 : -1;
        }
        return aValue < bValue ? 1 : -1;
      });

      // Apply pagination
      const total = games.length;
      const paginatedGames = games.slice(offset, offset + limit);

      // Simulate async operation
      const result = await simulateDbOperation(paginatedGames);

      const response = this.buildPaginationResponse(result, total, page, limit);

      this.success(res, response, 'Get list games successfully.');
    } catch (error) {
      this.error(res, 'Failed to get games list');
    }
  };

  /**
   * Create new game
   * POST /game
   */
  public createGame = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { fid, data } = req.body as CreateGameRequest;

      // Validate required fields
      const missing = this.validateRequired({ fid, data }, ['fid', 'data']);
      if (missing.length > 0) {
        this.badRequest(res, 'Missing required fields', {
          missing: missing.join(', ')
        });
        return;
      }

      // Simulate creating games
      const createdGames: Game[] = [];
      
      for (const gameData of data) {
        const newGame: Game = {
          id: mockDatabase.games.length + createdGames.length + 1,
          code: gameData.code,
          name: gameData.name,
          description: `Game created for FID: ${fid}`,
          icon: null,
          thumbnail: null,
          category_id: null,
          is_featured: false,
          play_count: 0,
          rating: 0,
          file_size: null,
          version: '1.0.0',
          status: 1,
          created_at: new Date(),
          updated_at: new Date(),
        };
        
        createdGames.push(newGame);
      }

      // Simulate async database operation
      const result = await simulateDbOperation(createdGames);

      this.success(res, result, 'Create game successfully.', 201);
    } catch (error) {
      this.error(res, 'Failed to create game');
    }
  };

  /**
   * Get top games by FID
   * GET /game/top/:fid
   */
  public getListTopGames = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { fid } = req.params as unknown as TopGamesParams;

      if (!fid) {
        this.badRequest(res, 'FID is required');
        return;
      }

      // Simulate getting top games for specific FID
      const topGames = mockDatabase.games
        .filter(game => game.is_featured || game.play_count > 10000)
        .sort((a, b) => b.play_count - a.play_count)
        .slice(0, 10);

      const result = await simulateDbOperation(topGames);

      this.success(res, result, 'Get list top games successfully.');
    } catch (error) {
      this.error(res, 'Failed to get top games');
    }
  };

  /**
   * Get user games by FID and UID
   * GET /game/top/:fid/:uid
   */
  public getListUserGames = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { fid, uid } = req.params as unknown as TopGamesParams;

      if (!fid) {
        this.badRequest(res, 'FID is required');
        return;
      }

      // Simulate getting user-specific games
      const userGames = mockDatabase.games
        .filter(game => game.play_count > 0) // Simulate user has played these games
        .slice(0, 5);

      const result = await simulateDbOperation(userGames);

      this.success(res, result, 'Get list user games successfully.');
    } catch (error) {
      this.error(res, 'Failed to get user games');
    }
  };

  /**
   * Get game detail by code
   * GET /game/:code
   */
  public getGameDetail = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { code } = req.params as unknown as GameDetailParams;

      if (!code) {
        this.badRequest(res, 'Game code is required');
        return;
      }

      const game = mockDatabase.games.find(g => g.code === code);

      if (!game) {
        this.notFound(res, 'Game not found');
        return;
      }

      const result = await simulateDbOperation(game);

      this.success(res, result, 'Get game detail successfully.');
    } catch (error) {
      this.error(res, 'Failed to get game detail');
    }
  };

  /**
   * Open game
   * POST /game/open
   */
  public openGame = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { game_id, fid, uid } = req.body as OpenGameRequest;

      if (!game_id) {
        this.badRequest(res, 'Game ID is required');
        return;
      }

      const game = mockDatabase.games.find(g => g.id === game_id);

      if (!game) {
        this.notFound(res, 'Game not found');
        return;
      }

      // Simulate opening game (increment play count)
      game.play_count += 1;

      const result = await simulateDbOperation({
        game_id,
        fid,
        uid,
        opened_at: new Date(),
        play_count: game.play_count
      });

      this.success(res, result, 'Open game successfully.');
    } catch (error) {
      this.error(res, 'Failed to open game');
    }
  };

  /**
   * Get RSS list
   * GET /rss
   */
  public getListRss = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { link } = req.query as unknown as RssRequest;

      if (!link) {
        this.badRequest(res, 'RSS link is required');
        return;
      }

      // Simulate RSS parsing
      const rssItems = [
        {
          title: 'Latest Game Update',
          link: 'https://example.com/news/latest-update',
          description: 'New features and improvements',
          published_at: new Date(),
          author: 'Game Team'
        },
        {
          title: 'New Game Release',
          link: 'https://example.com/news/new-release',
          description: 'Exciting new game available now',
          published_at: new Date(Date.now() - 86400000), // 1 day ago
          author: 'Game Team'
        }
      ];

      const result = await simulateDbOperation(rssItems);

      this.success(res, result, 'Get list rss successfully.');
    } catch (error) {
      this.error(res, 'Failed to get RSS list');
    }
  };
}

export default new GameController();
