"use strict";

const feedbackRepo = require("../../../repositories/feedback.repo");
const {validateInput} = require("../../../helpers");
const moment = require("moment-timezone");

class FeedbackService {
    static async sendFeedback(req) {
        validateInput(req)

        req.body.created_at = moment().tz('Asia/Ho_Chi_Minh').format('YYYY-MM-DD HH:mm:ss')

        await feedbackRepo.createFeedback(req.body)
    }
}

module.exports = FeedbackService;