"use strict";

const rssLinkModel = require("../models/rssLink.model");

class RssLinkRepo {
    static async getListRssLinks(filter, attributes, order, limit) {
        return await rssLinkModel.findAll({
            where: filter,
            limit: limit,
            order: order,
            attributes: attributes
        })
    }

    static async createRssLink(data) {
        const result = await rssLinkModel.create(data);
        return result.id;
    }

    static async deleteAllRssLink() {
        await rssLinkModel.truncate();
    }
}

module.exports = RssLinkRepo;