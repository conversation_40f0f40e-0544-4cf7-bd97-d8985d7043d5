"use strict";

const {OK} = require("../../../utils/successResponse");
const bannerService = require("../../../services/website/v1/banner.service");
const moment = require("moment-timezone");

class BannerController {
    getListBanners = async (req, res, next) => {
        new OK({
            message: 'Get list banners successfully.',
            metadata: await bannerService.getListBanners(req)
        }).send(res)
    }

    getListBannersV2 = async (req, res, next) => {
        new OK({
            message: 'Get list banners successfully.',
            metadata: await bannerService.getListBannersV2(req)
        }).send(res)
    }
}

module.exports = new BannerController();