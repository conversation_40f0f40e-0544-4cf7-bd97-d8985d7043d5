"use strict";

const IORedis = require("ioredis");
const {REDIS_HOST, REDIS_PASSWORD, REDIS_PORT, REDIS_DATABASE_NUMBER, REDIS_KEY_PREFIX} = process.env;

const redisClient = new IORedis({
    host: REDIS_HOST,
    port: REDIS_PORT,
    password: REDIS_PASSWORD,
    db: REDIS_DATABASE_NUMBER,
    keyPrefix: REDIS_KEY_PREFIX,
    lazyConnect: true, // Kết nối lười biếng, kết nối khi cần thiết
    maxRetriesPerRequest: 3, // Giới hạn số lần retry, tr<PERSON>h treo request
    enableAutoPipelining: true, // Gộp nhiều lệnh Redis vào 1 pipeline để tối ưu hiệu suất
    connectTimeout: 10000, // 10s timeout
    retryStrategy: (times) => {
        const delay = Math.min(times * 100, 2000);
        console.log(`🔁 Retry attempt #${times} in ${delay}ms`);
        return delay;
    },
    reconnectOnError: (err) => {
        const shouldReconnect = ['READONLY', 'ECONNRESET'].some(e => err.message.includes(e));
        if (shouldReconnect) {
            console.warn("⚠️ Reconnect due to error:", err.message);
        }
        return shouldReconnect;
    }
});

const connectRedis = async () => {
    if (redisClient.status !== "wait") {
        console.log("🔄 Redis is already connected or connecting...");
        return;
    }

    try {
        await redisClient.connect();
        console.log("✅ Redis client connected.");
    } catch (error) {
        console.error("❌ Unable to connect to Redis client:", error);
        throw error;
    }
};

redisClient.on("error", (err) => {
    console.error("❌ Redis error:", err);
});

redisClient.on("reconnecting", (time) => {
    console.log(`🔄 Redis reconnecting... (${time}ms)`);
});

module.exports = {
    redisClient,
    connectRedis
};