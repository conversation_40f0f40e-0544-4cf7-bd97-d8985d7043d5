"use strict";

const Queue = require('bull');

const updateLinkReportQueue = new Queue('updateLinkReportQueue', {
    prefix: `${process.env.REDIS_KEY_PREFIX}bull`,
    redis: {
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT,
        password: process.env.REDIS_PASSWORD,
    },
});

updateLinkReportQueue.process(async (job, done) => {
    const {link, type, fid, month, year} = job.data;

    try {
        await require('../services/website/v1/tracking.service').updateLinkReport(link, type, fid, month, year);
        done();
    } catch (error) {
        console.error(`Failed to update link report ${link}/${type}/${fid}/${month}/${year}::`, error.message);
        done(new Error(`Failed to update link report ${link}/${type}/${fid}/${month}/${year}:: ` + error.message));
    }
});

// Logging và xử lý sự kiện
// sendEmailQueue.on('completed', (job) => {
//     console.log(`Job ${job.id} has been completed`);
// });

updateLinkReportQueue.on('failed', (job, err) => {
    console.error(`Job ${job.id} failed with error: ${err.message}`);
});

updateLinkReportQueue.on('stalled', (job) => {
    console.warn(`Job ${job.id} has stalled and will be reprocessed`);
});

// sendEmailQueue.on('delayed', (job) => {
//     console.log(`Job ${job.id} has been delayed`);
// });

module.exports = {
    updateLinkReportQueue,
};