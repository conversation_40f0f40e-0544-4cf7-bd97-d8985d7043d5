"use strict";

const h5GameModel = require("../models/h5Game.model");

class H5GameRepo {
    static async getListH5Games(filter, attributes, order, limit) {
        return await h5GameModel.findAll({
            where: filter,
            limit: limit,
            order: order,
            attributes: attributes
        })
    }

    static async getListH5GamesPagination(filter, include, attributes, order, page, limit) {
        const {count, rows} = await h5GameModel.findAndCountAll({
            where: filter,
            include: include,
            attributes: attributes,
            order: order,
            offset: (page - 1) * limit,
            limit: limit
        })

        return {
            total_items: count,
            items_per_page: limit,
            current_page: page,
            total_pages: Math.ceil(count / limit),
            items: rows
        };
    }

    static async getH5GameDetail(filter, include, attributes) {
        return await h5GameModel.findOne({
            where: filter,
            include: include,
            attributes: attributes
        })
    }

    static async createH5Game(data) {
        const result = await h5GameModel.create(data);
        return result.id;
    }

    static async updateH5Game(model, data, filter = {}) {
        await model.update(data, {
            where: filter
        });
    }

    static async updateH5GameWithConditions(data, filter = {}) {
        await h5GameModel.update(data, {
            where: filter
        });
    }
}

module.exports = H5GameRepo;