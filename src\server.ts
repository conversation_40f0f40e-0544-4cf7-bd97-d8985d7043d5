import app from './app';
import { logger } from '@/middlewares/logging.middleware';

/**
 * Server Configuration
 */
const PORT = process.env.APP_PORT || 3004;
const HOST = process.env.HOST || '0.0.0.0';
const NODE_ENV = process.env.NODE_ENV || 'development';

/**
 * Database Connection Simulation
 * In real implementation, you would connect to actual databases here
 */
const connectDatabase = async (): Promise<void> => {
  try {
    // Simulate database connection
    logger.info('Connecting to main database...');
    await new Promise(resolve => setTimeout(resolve, 100));
    logger.info('Main database connected successfully');
  } catch (error) {
    logger.error('Failed to connect to main database', { error });
    throw error;
  }
};

const connectLogDatabase = async (): Promise<void> => {
  try {
    // Simulate log database connection
    logger.info('Connecting to log database...');
    await new Promise(resolve => setTimeout(resolve, 50));
    logger.info('Log database connected successfully');
  } catch (error) {
    logger.error('Failed to connect to log database', { error });
    throw error;
  }
};

const connectRedis = async (): Promise<void> => {
  try {
    // Simulate Redis connection
    logger.info('Connecting to Redis...');
    await new Promise(resolve => setTimeout(resolve, 50));
    logger.info('Redis connected successfully');
  } catch (error) {
    logger.error('Failed to connect to Redis', { error });
    throw error;
  }
};

/**
 * Start Server
 */
const startServer = async (): Promise<void> => {
  try {
    logger.info('Starting FUS App Backend v2...', {
      nodeEnv: NODE_ENV,
      port: PORT,
      host: HOST,
    });

    // Connect to databases
    await connectDatabase();
    await connectLogDatabase();
    await connectRedis();

    // Start HTTP server
    const server = app.listen(PORT, HOST, () => {
      logger.info(`🚀 Server is running on http://${HOST}:${PORT}`, {
        environment: NODE_ENV,
        port: PORT,
        host: HOST,
        processId: process.pid,
      });

      // Log available endpoints
      logger.info('Available endpoints:', {
        health: `http://${HOST}:${PORT}/health`,
        apiInfo: `http://${HOST}:${PORT}/api/info`,
        games: `http://${HOST}:${PORT}/game`,
        h5Games: `http://${HOST}:${PORT}/game-h5`,
        tracking: `http://${HOST}:${PORT}/tracking`,
      });
    });

    // Handle server errors
    server.on('error', (error: NodeJS.ErrnoException) => {
      if (error.syscall !== 'listen') {
        throw error;
      }

      const bind = typeof PORT === 'string' ? `Pipe ${PORT}` : `Port ${PORT}`;

      switch (error.code) {
        case 'EACCES':
          logger.error(`${bind} requires elevated privileges`);
          process.exit(1);
          break;
        case 'EADDRINUSE':
          logger.error(`${bind} is already in use`);
          process.exit(1);
          break;
        default:
          throw error;
      }
    });

    // Graceful shutdown
    const gracefulShutdown = (signal: string) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);
      
      server.close((err) => {
        if (err) {
          logger.error('Error during server shutdown', { error: err });
          process.exit(1);
        }
        
        logger.info('Server closed successfully');
        
        // Close database connections
        // In real implementation, close actual database connections here
        logger.info('Database connections closed');
        
        process.exit(0);
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('Failed to start server', { error });
    process.exit(1);
  }
};

// Start the server
startServer();
