"use strict";

const {redisClient} = require("../dbs/ioredis.db");

class RedisService {
    static async getListKeys(pattern) {
        let cursor = "0";
        let keys = [];

        const prefix = redisClient.options.keyPrefix || "";
        const fullPattern = `${prefix}${pattern}`;

        do {
            const reply = await redisClient.scan(cursor, "MATCH", fullPattern, "COUNT", 100);
            cursor = reply[0];
            keys.push(...reply[1]);
        } while (cursor !== "0");

        return keys;
    }

    static async getKeys(keys) {
        if (!keys || keys.length === 0) return [];
        try {
            return await redisClient.mget(keys);
        } catch (error) {
            console.error(`❌ Redis getKeys() failed`, error);
            return [];
        }
    }

    static async getKey(key) {
        try {
            return await redisClient.get(key);
        } catch (error) {
            console.error(`❌ Redis getKey() failed for key: ${key}`, error);
            return null;
        }
    }

    static async pushKeyToRedis(key, value, expireTime = 600) {
        const pipeline = redisClient.pipeline();
        pipeline.set(key, value);
        pipeline.expire(key, expireTime);
        await pipeline.exec();
    }

    static async pushKeyToListsRedis(key, value, expireTime = 600) {
        const pipeline = redisClient.pipeline();
        pipeline.rpush(key, value);
        pipeline.expire(key, expireTime);
        await pipeline.exec();
    }

    static async pushKeyToSortedSet(key, value, score, expireTime = 600) {
        const transaction = redisClient.multi();
        transaction.zadd(key, score, value);
        transaction.expire(key, expireTime);
        await transaction.exec();
    }

    static async pushHashToRedis(key, field, value, expireTime = 600) {
        const pipeline = redisClient.pipeline();
        pipeline.hset(key, field, value);
        pipeline.expire(key, expireTime);
        await pipeline.exec();
    }

    static async deleteKeyFromRedis(key) {
        try {
            await redisClient.del(key);
        } catch (error) {
            console.error(`❌ Redis deleteKeyFromRedis() failed for key: ${key}`, error);
        }
    }

    static async deleteKeysByPattern(pattern) {
        try {
            const keys = await this.getListKeys(pattern); // Lấy danh sách key theo pattern
            if (keys.length > 0) {
                const keysWithoutPrefix = keys.map(key => key.replace(process.env.REDIS_KEY_PREFIX, ''));

                await redisClient.del(...keysWithoutPrefix); // Xóa tất cả các key tìm thấy
                console.log(`✅ Deleted ${keys.length} keys matching pattern: ${pattern}`);
            } else {
                console.log(`ℹ️ No keys found for pattern: ${pattern}`);
            }
        } catch (error) {
            console.error(`❌ Redis deleteKeysByPattern() failed for pattern: ${pattern}`, error);
        }
    }
}

module.exports = RedisService;
