"use strict";

const express = require('express');
const {asyncVersionHandler} = require("../../helpers");

const router = express.Router();

const controllerName = 'h5Game'

router.get('/', asyncVersionHandler(controllerName, 'getListH5Games'));
router.get('/category', asyncVersionHandler(controllerName, 'getListH5GameCategories'));
router.get('/trending', asyncVersionHandler(controllerName, 'getListTrendingH5Games'));
router.get('/:fid/:uid', asyncVersionHandler(controllerName, 'getListUserH5Games'));

module.exports = router;