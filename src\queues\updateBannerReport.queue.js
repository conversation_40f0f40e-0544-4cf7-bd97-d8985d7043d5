"use strict";

const Queue = require('bull');

const updateBannerReportQueue = new Queue('updateBannerReportQueue', {
    prefix: `${process.env.REDIS_KEY_PREFIX}bull`,
    redis: {
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT,
        password: process.env.REDIS_PASSWORD,
    },
});

updateBannerReportQueue.process(async (job, done) => {
    const {item_id, fid, type, currentDate} = job.data;

    try {
        await require('../services/website/v1/tracking.service').updateBannerReport(item_id, fid, type, currentDate);
        done();
    } catch (error) {
        console.error(`Failed to update banner report ${item_id}/${fid}/${type}/${currentDate}::`, error.message);
        done(new Error(`Failed to update banner report ${item_id}/${fid}/${type}/${currentDate}:: ` + error.message));
    }
});

// Logging và xử lý sự kiện
// sendEmailQueue.on('completed', (job) => {
//     console.log(`Job ${job.id} has been completed`);
// });

updateBannerReportQueue.on('failed', (job, err) => {
    console.error(`Job ${job.id} failed with error: ${err.message}`);
});

updateBannerReportQueue.on('stalled', (job) => {
    console.warn(`Job ${job.id} has stalled and will be reprocessed`);
});

// sendEmailQueue.on('delayed', (job) => {
//     console.log(`Job ${job.id} has been delayed`);
// });

// Xóa toàn bộ queue và khởi động lại
// async function obliterateQueue() {
//     await updateBannerReportQueue.obliterate({ force: true });
//     console.log("Queue has been obliterated!");
// }

module.exports = {
    updateBannerReportQueue,
};