"use strict";

const {Op, literal} = require("sequelize");
const h5GameRepo = require("../../../repositories/h5Game.repo");
const h5GameCategoryRepo = require("../../../repositories/h5GameCategory.repo");
const userH5GameReportRepo = require("../../../repositories/userH5GameReport.repo");
const {NotFoundError} = require("../../../utils/errorResponse");
const {defaultStatus} = require("../../../enums/objStatus");
const h5GameModel = require("../../../models/h5Game.model");
const redisService = require("../../redis.service");

class H5GameService {
    static async getListH5Games(req) {
        const limit = req.query.limit ? parseInt(req.query.limit) : 14;
        const page = req.query.page ? parseInt(req.query.page) : 1;
        const searchKey = req.query.search ? req.query.search : null;
        const category = req.query.category ? req.query.category : null;

        const redisKey = `cache:h5_games:${category}:${limit}:${page}:${searchKey}`

        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        let searchCondition = {
            name: {
                [Op.not]: null
            }
        }
        let categoryCondition = {}

        if (searchKey) {
            searchCondition = {
                name: {
                    [Op.like]: `%${searchKey}%`
                }
            }
        }

        if (category) {
            const existsCate = await h5GameCategoryRepo.getH5GameCategoryDetail({
                    slug: category,
                    status: defaultStatus.ACTIVE,
                    deleted_at: null,
                    deleted_by: null,
                },
                null,
                ['id'])

            if (existsCate) {
                categoryCondition = {
                    id: {
                        [Op.in]: literal(`(SELECT game_id FROM h5_game_category WHERE category_id = ${existsCate.id})`)
                    },
                }
            } else {
                // throw new NotFoundError('Danh mục game không tồn tại!')
                return []
            }
        }

        const h5Games = await h5GameRepo.getListH5GamesPagination({
                ...categoryCondition,
                ...searchCondition,
                code: {
                    [Op.not]: null
                },
                logo: {
                    [Op.not]: null
                },
                status: defaultStatus.ACTIVE,
                deleted_at: null,
                deleted_by: null,
            },
            null,
            ['code', 'name', 'logo', 'link', [literal('total_fake_play + total_real_play'), 'total_play']],
            [['created_at', 'DESC']],
            page,
            limit)

        await redisService.pushKeyToRedis(redisKey, JSON.stringify(h5Games), searchKey ? 60 : 86400) // 1 ngày

        return h5Games
    }

    static async getListH5GameCategories(req) {
        const redisKey = `cache:h5_game_categories`

        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        const h5GameCategories = await h5GameCategoryRepo.getListH5GameCategories({
                status: defaultStatus.ACTIVE,
                deleted_at: null,
                deleted_by: null,
            },
            ['name', 'slug', 'icon'],
            [['created_at', 'DESC']],
            null)

        await redisService.pushKeyToRedis(redisKey, JSON.stringify(h5GameCategories), 86400) // 1 ngày

        return h5GameCategories
    }

    static async getListTrendingH5Games(req) {
        const redisKey = `cache:trending_h5_games`

        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        const limit = req.query.limit ? parseInt(req.query.limit) : 5;

        const result = await h5GameRepo.getListH5Games({
                code: {
                    [Op.not]: null
                },
                name: {
                    [Op.not]: null
                },
                logo: {
                    [Op.not]: null
                },
                status: defaultStatus.ACTIVE,
                deleted_at: null,
                deleted_by: null,
            },
            ['code', 'name', 'logo', 'link', [literal('total_fake_play + total_real_play'), 'total_play']],
            [['total_play', 'DESC']],
            limit)

        await redisService.pushKeyToRedis(redisKey, JSON.stringify(result), 1800) // 30p

        return result
    }

    static async getListUserH5Games(req) {
        const {fid, uid} = req.params;
        const limit = req.query.limit ? parseInt(req.query.limit) : 5;

        const redisKey = `cache:user_h5_games:${fid}:${uid}`
        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        const data = await userH5GameReportRepo.getListUserH5GameReports(
            {fid, uid},
            [{
                model: h5GameModel,
                where: {
                    status: defaultStatus.ACTIVE,
                    deleted_at: null,
                    deleted_by: null,
                },
                required: true,
                as: 'detail',
                attributes: ['code', 'name', 'logo', 'link']
            }],
            ['created_at'],
            [['created_at', 'DESC']],
            limit
        );

        const formatData = JSON.parse(JSON.stringify(data))

        const result = formatData.map(item => ({
            created_at: item.created_at,
            ...item.detail
        }));

        await redisService.pushKeyToRedis(redisKey, JSON.stringify(result), 300) // 5p

        return result
    }
}

module.exports = H5GameService