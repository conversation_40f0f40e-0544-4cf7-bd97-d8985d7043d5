"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const playlistVideoModel = sequelize.define('playlist_video', {
    playlist_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    video_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
}, {
    tableName: 'playlist_video',
    timestamps: false,
    underscored: true,
    freezeTableName: true,
    id: false,
});

module.exports = playlistVideoModel;