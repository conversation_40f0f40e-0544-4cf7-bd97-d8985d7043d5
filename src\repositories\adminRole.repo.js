"use strict";

const adminRoleModel = require("../models/adminRole.model");

class AdminRoleRepo {

    static async getListAdminRoles(filter, attributes) {
        return await adminRoleModel.findAll({
            where: filter,
            attributes: attributes,
            raw: true
        });
    }

    static async createAdminRole(data) {
        await adminRoleModel.create(data);
    }

    static async deleteAdminRole(filter) {
        await adminRoleModel.destroy({
            where: filter
        });
    }
}

module.exports = AdminRoleRepo;