"use strict";

require('dotenv').config()
const express = require('express')
const compression = require('compression')
const cors = require('cors')
const multer = require('multer')
const {default: helmet} = require('helmet')
const cookieParser = require('cookie-parser')
const {corsOptions, limiter} = require("./configs/app.config");
const routes = require("./routes/index");
const {notFound, errorHandler} = require("./middlewares/error.mid");
const {serverAdapter} = require("./queues");

const app = express()

app.set("trust proxy", 1);

// init middleware
app.use(cookieParser());
app.use(helmet());
app.use(compression())
app.use(cors(corsOptions))
app.use(limiter)
app.use(multer().none())
app.use(express.json())
app.use(express.urlencoded({
    extended: true
}))

// init model
require('./models/index');
const {verifyBullDashboard} = require("./middlewares/website/verifyBullDashboard.mid");

// init router
app.use(routes);
app.use('/admin/queues', verifyBullDashboard, serverAdapter.getRouter())

// handle error
app.use(notFound);
app.use(errorHandler);

module.exports = app;