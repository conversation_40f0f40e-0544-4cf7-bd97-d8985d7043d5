"use strict";

const Queue = require('bull');

const updateLocationQueue = new Queue('updateLocationQueue', {
    prefix: `${process.env.REDIS_KEY_PREFIX}bull`,
    redis: {
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT,
        password: process.env.REDIS_PASSWORD,
    },
});

updateLocationQueue.process(async (job, done) => {
    const {fid, fuid} = job.data;

    try {
        await require('../services/website/v1/location.service').updateLocation(fid, fuid);
        done();
    } catch (error) {
        console.error(`Failed to update location ${fid}/${fuid}::`, error.message);
        done(new Error(`Failed to update location ${fid}/${fuid}:: ` + error.message));
    }
});

// Logging và xử lý sự kiện
// sendEmailQueue.on('completed', (job) => {
//     console.log(`Job ${job.id} has been completed`);
// });

updateLocationQueue.on('failed', (job, err) => {
    console.error(`Job ${job.id} failed with error: ${err.message}`);
});

updateLocationQueue.on('stalled', (job) => {
    console.warn(`Job ${job.id} has stalled and will be reprocessed`);
});

// sendEmailQueue.on('delayed', (job) => {
//     console.log(`Job ${job.id} has been delayed`);
// });

// Xóa toàn bộ queue và khởi động lại
// async function obliterateQueue() {
//     await updateLocationQueue.obliterate({ force: true });
//     console.log("Queue has been obliterated!");
// }

module.exports = {
    updateLocationQueue,
};