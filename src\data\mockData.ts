import { Game, H5G<PERSON>, <PERSON>, BannerV2, Video, Playlist, News } from '@/types/api';

// Mock Games Data
export const mockGames: Game[] = [
  {
    id: 1,
    code: 'GAME001',
    name: 'Adventure Quest',
    description: 'An epic adventure game with stunning graphics',
    icon: 'https://example.com/icons/adventure-quest.png',
    thumbnail: 'https://example.com/thumbnails/adventure-quest.jpg',
    category_id: 1,
    is_featured: true,
    play_count: 15420,
    rating: 4.5,
    file_size: 125000000, // 125MB
    version: '1.2.3',
    status: 1,
    created_at: new Date('2024-01-15'),
    updated_at: new Date('2024-01-20'),
  },
  {
    id: 2,
    code: 'GAME002',
    name: 'Racing Thunder',
    description: 'High-speed racing game with realistic physics',
    icon: 'https://example.com/icons/racing-thunder.png',
    thumbnail: 'https://example.com/thumbnails/racing-thunder.jpg',
    category_id: 2,
    is_featured: false,
    play_count: 8750,
    rating: 4.2,
    file_size: 89000000, // 89MB
    version: '2.1.0',
    status: 1,
    created_at: new Date('2024-01-10'),
    updated_at: new Date('2024-01-18'),
  },
  {
    id: 3,
    code: 'GAME003',
    name: 'Puzzle Master',
    description: 'Brain-teasing puzzle game for all ages',
    icon: 'https://example.com/icons/puzzle-master.png',
    thumbnail: 'https://example.com/thumbnails/puzzle-master.jpg',
    category_id: 3,
    is_featured: true,
    play_count: 23100,
    rating: 4.8,
    file_size: 45000000, // 45MB
    version: '1.0.5',
    status: 1,
    created_at: new Date('2024-01-05'),
    updated_at: new Date('2024-01-22'),
  },
];

// Mock H5 Games Data
export const mockH5Games: H5Game[] = [
  {
    id: 1,
    name: 'Bubble Shooter',
    slug: 'bubble-shooter',
    description: 'Classic bubble shooting game',
    thumbnail: 'https://example.com/h5games/bubble-shooter.jpg',
    game_url: 'https://games.example.com/bubble-shooter',
    is_trending: true,
    play_count: 45200,
    rating: 4.3,
    status: 1,
    created_at: new Date('2024-01-12'),
    updated_at: new Date('2024-01-19'),
  },
  {
    id: 2,
    name: 'Snake Classic',
    slug: 'snake-classic',
    description: 'The classic snake game reimagined',
    thumbnail: 'https://example.com/h5games/snake-classic.jpg',
    game_url: 'https://games.example.com/snake-classic',
    is_trending: false,
    play_count: 12800,
    rating: 4.0,
    status: 1,
    created_at: new Date('2024-01-08'),
    updated_at: new Date('2024-01-16'),
  },
];

// Mock Banners Data
export const mockBanners: Banner[] = [
  {
    id: 1,
    title: 'New Game Launch',
    image: 'https://example.com/banners/new-game-launch.jpg',
    redirect_link: 'https://example.com/games/new-launch',
    position_id: 1,
    start_date: new Date('2024-01-01'),
    end_date: new Date('2024-02-01'),
    sort_order: 1,
    click_count: 1250,
    status: 1,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-15'),
  },
];

// Mock Banners V2 Data
export const mockBannersV2: BannerV2[] = [
  {
    id: 1,
    image: 'https://example.com/banners/v2/promo-banner.jpg',
    apply_date: new Date('2024-01-01'),
    display_type: 1,
    destination_type: 1,
    active_click: true,
    sort: 1,
    redirect_link: {
      url: 'https://example.com/promo',
      target: '_blank'
    },
    status: 1,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-15'),
  },
];

// Mock Videos Data
export const mockVideos: Video[] = [
  {
    id: 1,
    title: 'Game Tutorial: Getting Started',
    description: 'Learn the basics of our most popular games',
    thumbnail: 'https://example.com/videos/tutorial-thumb.jpg',
    video_url: 'https://example.com/videos/tutorial.mp4',
    duration: 300, // 5 minutes
    view_count: 5420,
    like_count: 234,
    category_id: 1,
    is_featured: true,
    status: 1,
    created_at: new Date('2024-01-10'),
    updated_at: new Date('2024-01-18'),
  },
];

// Mock Playlists Data
export const mockPlaylists: Playlist[] = [
  {
    id: 1,
    name: 'Top Gaming Videos',
    slug: 'top-gaming-videos',
    icon: 'https://example.com/playlists/top-gaming.png',
    description: 'Collection of the best gaming videos',
    sort: 1,
    status: 1,
    created_at: new Date('2024-01-05'),
    updated_at: new Date('2024-01-20'),
  },
];

// Mock News Data
export const mockNews: News[] = [
  {
    id: 1,
    title: 'New Game Update Released',
    slug: 'new-game-update-released',
    content: 'We are excited to announce the latest update to our gaming platform...',
    excerpt: 'Latest update brings new features and improvements',
    thumbnail: 'https://example.com/news/update-news.jpg',
    author: 'Game Team',
    published_at: new Date('2024-01-20'),
    view_count: 1250,
    is_featured: true,
    is_flash_news: false,
    status: 1,
    created_at: new Date('2024-01-20'),
    updated_at: new Date('2024-01-20'),
  },
];

// Helper functions to simulate database operations
export const mockDatabase = {
  games: mockGames,
  h5Games: mockH5Games,
  banners: mockBanners,
  bannersV2: mockBannersV2,
  videos: mockVideos,
  playlists: mockPlaylists,
  news: mockNews,
};

// Simulate async database operations
export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const simulateDbOperation = async <T>(data: T, delayMs: number = 100): Promise<T> => {
  await delay(delayMs);
  return data;
};
