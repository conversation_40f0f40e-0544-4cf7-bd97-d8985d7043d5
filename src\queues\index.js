"use strict";

const {ExpressAdapter} = require("@bull-board/express");
const {createBullBoard} = require("@bull-board/api");
const {BullAdapter} = require("@bull-board/api/bullAdapter");
const {updateGameReportQueue} = require("./updateGameReport.queue");
const {updateLinkReportQueue} = require("./updateLinkReport.queue");
const {updateBannerReportQueue} = require("./updateBannerReport.queue");
const {updateLogEventQueue} = require("./updateLogEvent.queue");
const {updateLocationQueue} = require("./updateLocation.queue");
const serverAdapter = new ExpressAdapter();

serverAdapter.setBasePath('/admin/queues');

createBullBoard({
    queues: [
        new BullAdapter(updateGameReportQueue),
        new BullAdapter(updateLinkReportQueue),
        new BullAdapter(updateBannerReportQueue),
        new BullAdapter(updateLogEventQueue),
        new BullAdapter(updateLocationQueue),
    ],
    serverAdapter: serverAdapter,
});

module.exports = {
    serverAdapter,
};