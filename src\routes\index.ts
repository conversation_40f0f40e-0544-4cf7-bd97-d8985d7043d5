import { Router } from 'express';
import { getApiVersion } from '@/middlewares/auth.middleware';
import websiteRoutes from './website';

const router = Router();

/**
 * Main API Routes
 */

// Apply API version middleware to all routes
router.use(getApiVersion);

// Website routes
router.use('/', websiteRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'success',
    message: 'Server is healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
  });
});

// API info endpoint
router.get('/api/info', (req, res) => {
  res.json({
    status: 'success',
    message: 'FUS App Backend API v2',
    version: '2.0.0',
    documentation: '/api/docs',
    endpoints: {
      games: '/game',
      h5Games: '/game-h5',
      tracking: '/tracking',
      health: '/health',
    },
  });
});

export default router;
