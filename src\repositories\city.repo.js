"use strict";

const cityModel = require("../models/city.model");

class CityRepo {
    static async getListCities(filter, attributes, order, limit) {
        return await cityModel.findAll({
            where: filter,
            limit: limit,
            order: order,
            attributes: attributes
        })
    }

    static async getListCitiesPagination(filter, include, attributes, order, page, limit) {
        const {count, rows} = await cityModel.findAndCountAll({
            where: filter,
            include: include,
            attributes: attributes,
            order: order,
            offset: (page - 1) * limit,
            limit: limit
        })

        return {
            total_items: count,
            items_per_page: limit,
            current_page: page,
            total_pages: Math.ceil(count / limit),
            items: rows
        };
    }

    static async getCityDetail(filter, include, attributes) {
        return await cityModel.findOne({
            where: filter,
            include: include,
            attributes: attributes
        })
    }

    static async createCity(data) {
        const result = await cityModel.create(data);
        return result.id;
    }

    static async updateCity(model, data, filter = {}) {
        await model.update(data, {
            where: filter
        });
    }

    static async updateCityWithConditions(data, filter = {}) {
        await cityModel.update(data, {
            where: filter
        });
    }
}

module.exports = CityRepo;