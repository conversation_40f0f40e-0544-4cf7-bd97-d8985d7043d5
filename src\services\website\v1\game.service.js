"use strict";

const Parser = require("rss-parser");
const parser = new Parser({
    headers: {
        Accept: 'application/rss+xml, application/xml',
    },
});
const {Op, fn, col} = require("sequelize");
const redisService = require("../../redis.service");
const bannerV2ClientService = require("../v1/banner.service");
const gameRepo = require("../../../repositories/game.repo");
const h5GameRepo = require("../../../repositories/h5Game.repo");
const gameReportRepo = require("../../../repositories/gameReport.repo");
const userGameReportRepo = require("../../../repositories/userGameReport.repo");
const userGameH5ReportRepo = require("../../../repositories/userH5GameReport.repo");
const selfGameRepo = require("../../../repositories/selfGame.repo");
const locationRepo = require("../../../repositories/location.repo");
const gameModel = require("../../../models/game.model");
const selfGameModel = require("../../../models/selfGame.model");
const h5GameModel = require("../../../models/h5Game.model");
const {validateInput} = require("../../../helpers");
const moment = require("moment-timezone");
const {updateGameReportQueue} = require("../../../queues/updateGameReport.queue");
const {defaultStatus} = require("../../../enums/objStatus");

class GameService {
    static async getListGames(req) {
        const fid = req.query.fid || 0
        const redisKey = `cache:games:${fid}`

        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        const selfGames = await selfGameRepo.getListSelfGames({
                code: {
                    [Op.not]: null
                },
                name: {
                    [Op.not]: null
                },
                fid
            },
            ['code', 'name', 'logo'], [['id', 'ASC']],
            null)

        const commonGames = await gameRepo.getListGames({
                code: {
                    [Op.not]: null
                },
                name: {
                    [Op.not]: null
                },
            },
            ['code', 'name', 'logo'],
            [['id', 'ASC']],
            null)

        const result = [...selfGames, ...commonGames];

        await redisService.pushKeyToRedis(redisKey, JSON.stringify(result), 86400) // 1 ngày

        return result;
    }

    static async getListTopGames(req) {
        const fid = req.params.fid || 0
        const limit = req.query.limit ? parseInt(req.query.limit) : 6;

        const redisKey = `cache:top_games:${fid}`

        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        let result = []

        const [
            topGames,
            iconGameHot4,
            iconGameHot5,
            iconGameHot6
        ] = await Promise.all([
            gameReportRepo.getListGameReports(
                {
                    fid,
                    code: {
                        [Op.and]: [
                            {[Op.notLike]: 'self%'},
                            {[Op.notLike]: 'gh5%'},
                        ],
                    }
                },
                [
                    {
                        model: gameModel,
                        attributes: ['id', 'code', 'name', 'logo'],
                        required: true
                    },
                    // {
                    //     model: selfGameModel,
                    //     where: {fid},
                    //     attributes: ['id', 'fid', 'code', 'name', 'logo'],
                    //     required: false
                    // },
                    // {
                    //     model: h5GameModel,
                    //     where: {
                    //         status: defaultStatus.ACTIVE,
                    //         deleted_at: null,
                    //         deleted_by: null,
                    //     },
                    //     attributes: ['id', 'code', 'name', 'logo', 'link'],
                    //     required: false
                    // }
                ],
                ['code', [fn('SUM', col('total_open')), 'total_open']],
                ['code', 'game.id', 'game.code',
                    // 'self_game.id', 'self_game.code',
                    // 'h5_game.id', 'h5_game.code'
                ],
                [[fn('SUM', col('total_open')), 'DESC']],
                limit
            ),
            bannerV2ClientService.getBannerV2ByPosition(fid, 6, 1),
            bannerV2ClientService.getBannerV2ByPosition(fid, 7, 1),
            bannerV2ClientService.getBannerV2ByPosition(fid, 8, 1),
        ]);

        const hotGameIcons = [iconGameHot4, iconGameHot5, iconGameHot6]
        const hotGameCodes = hotGameIcons.filter(item => item !== null).map(item => {
            if (item?.redirect_link) {
                return item.redirect_link.code
            }
        })

        result = topGames.filter(item => !hotGameCodes.includes(item.code)).map(top => ({
            code: top.code,
            // name: top.game?.name || top.self_game?.name || top.h5_game?.name || null,
            // logo: top.game?.logo || top.self_game?.logo || top.h5_game?.logo || null,
            name: top.game?.name,
            logo: top.game?.logo,
            // link: top.h5_game?.link || undefined,
        }));

        // Insert icons at positions
        hotGameIcons.forEach((icon, idx) => {
            if (icon) {
                const formatData = {
                    id: icon.id,
                    image: icon.image,
                    redirect_link: icon.redirect_link,
                }
                const insertIndex = 3 + idx;
                if (result.length > insertIndex) {
                    result.splice(insertIndex, 0, formatData);
                } else {
                    result.push(formatData);
                }
            }
        });

        result = result.slice(0, 6)

        // Nếu không có icon game hot thì mới cache (để đảm bảo tracking chuẩn)
        if (!iconGameHot4 && !iconGameHot5 && !iconGameHot6) {
            await redisService.pushKeyToRedis(redisKey, JSON.stringify(result), 1800) // 30p
        }

        return result
    }

    static insertAtOrPush(array, index, item) {
        if (!item) return
        if (array.length > index) {
            array.splice(index, 0, item)
        } else {
            array.push(item)
        }
    }

    static async getListUserGames(req) {
        const {fid, uid} = req.params;

        const redisKey = `cache:user_games:${fid}:${uid}`

        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        const userGameReport = await userGameReportRepo.getUserGameReportDetail({fid, uid}, ['games']);
        const listUserGames = userGameReport?.games ? JSON.parse(userGameReport.games) : [];

        if (!listUserGames.length) return [];

        const categorizedGames = {
            common: [],
            self: [],
            h5: [],
        };

        for (const code of listUserGames) {
            if (code.startsWith('self')) categorizedGames.self.push(code);
            else if (code.startsWith('gh5')) categorizedGames.h5.push(code);
            else categorizedGames.common.push(code);
        }

        const [commonGameInfo, selfGameInfo, h5GameInfo] = await Promise.all([
            categorizedGames.common.length
                ? gameRepo.getListGames({code: {[Op.in]: categorizedGames.common}}, ['code', 'name', 'logo'])
                : [],
            categorizedGames.self.length
                ? selfGameRepo.getListSelfGames({
                    fid,
                    code: {[Op.in]: categorizedGames.self}
                }, ['code', 'name', 'logo'])
                : [],
            categorizedGames.h5.length
                ? h5GameRepo.getListH5Games(
                    {
                        code: {[Op.in]: categorizedGames.h5},
                        status: defaultStatus.ACTIVE,
                        deleted_at: null,
                        deleted_by: null,
                    },
                    ['code', 'name', 'logo', 'link']
                )
                : [],
        ]);

        const allGameInfo = [...commonGameInfo, ...selfGameInfo, ...h5GameInfo];

        const gameInfoMap = allGameInfo.reduce((acc, {code, name, logo, link}) => {
            acc[code.toLowerCase()] = {name, logo, link};
            return acc;
        }, {});

        const result = listUserGames.map(code => {
            const game = gameInfoMap[code.toLowerCase()] || {};
            return {
                code,
                name: game.name || null,
                logo: game.logo || null,
                link: game.link || undefined,
            };
        });

        await redisService.pushKeyToRedis(redisKey, JSON.stringify(result), 86400) // 1 ngày

        return result
    }

    static async getGameDetail(req) {
        const gameCode = req.params.code

        const game = await gameRepo.getGameDetail({
                code: gameCode,
            },
            null,
            ['code', 'name', 'logo'])
        if (!game) {
            // throw new NotFoundError(null, 'Not found');
            return []
        }

        return game
    }

    static async openGame(req) {
        const {fid, code, uid} = req.body;
        const currentMonth = moment().tz("Asia/Ho_Chi_Minh").month() + 1;
        const currentYear = moment().tz("Asia/Ho_Chi_Minh").year();

        // Xoá cache
        const redisKey = `cache:user_games:${fid}:${uid}`
        await redisService.deleteKeyFromRedis(redisKey);

        if (code) {
            updateGameReportQueue.add({
                fid: fid || 0,
                code,
                uid: uid || 0,
                month: currentMonth,
                year: currentYear
            }, {
                removeOnComplete: true,
            });
        }
    }

    static async createGame(req) {
        validateInput(req);

        const {data, fid} = req.body;
        const currentTime = moment().tz('Asia/Ho_Chi_Minh').format('YYYY-MM-DD HH:mm:ss');

        if (fid !== 0 && fid !== '0') {
            const existsLocation = await locationRepo.getLocationDetail({
                    fid,
                    status: defaultStatus.ACTIVE
                },
                ['id']
            )

            if (existsLocation) {
                const [selfGameArr, newGameArr] = data.reduce(
                    ([selfArr, newArr], item) => {
                        item.code.startsWith("self") ? selfArr.push({
                            fid,
                            created_at: currentTime,
                            ...item
                        }) : newArr.push({
                            created_at: currentTime,
                            ...item
                        });
                        return [selfArr, newArr];
                    },
                    [[], []]
                );

                const [existingSelfGames, existingNewGames] = await Promise.all([
                    selfGameRepo.getListSelfGames({code: selfGameArr.map(item => item.code), fid}, ['code']),
                    gameRepo.getListGames({code: newGameArr.map(item => item.code)}, ['code']),
                ]);

                const existingSelfGameSet = new Set(existingSelfGames.map(game => game.code));
                const existingNewGameSet = new Set(existingNewGames.map(game => game.code));

                const selfGames = selfGameArr.filter(item => !existingSelfGameSet.has(item.code));
                const newGames = newGameArr.filter(item => !existingNewGameSet.has(item.code));

                await Promise.all([
                    selfGames.length > 0 ? selfGameRepo.bulkCreateSelfGames(selfGames) : null,
                    newGames.length > 0 ? gameRepo.bulkCreateGames(newGames) : null
                ]);
            }
        }
    }

    static async updateGameReport(fid, code, uid, month, year) {
        const lowerCode = code.toLowerCase()
        const upperCode = code.toUpperCase()
        const currentTime = moment().tz('Asia/Ho_Chi_Minh').format('YYYY-MM-DD HH:mm:ss');

        let existH5Game, existUserH5GameReports
        if (lowerCode.startsWith("gh5")) {
            [existH5Game, existUserH5GameReports] = await Promise.all([
                h5GameRepo.getH5GameDetail({
                    code: lowerCode,
                    status: defaultStatus.ACTIVE,
                    deleted_at: null,
                    deleted_by: null
                }, null, ['id', 'total_real_play']),
                userGameH5ReportRepo.getListUserH5GameReports({
                    fid,
                    code: lowerCode
                }, null, ['code', 'fid'], [['created_at', 'DESC']], null)
            ]);
        }

        const [existGameReport, existUserGameReport] = await Promise.all([
            gameReportRepo.getGameReportDetail({
                fid,
                code: lowerCode,
                month,
                year
            }, ['code', 'fid', 'month', 'year', 'total_open']),
            userGameReportRepo.getUserGameReportDetail({fid, uid}, ['fid', 'uid', 'games'])
        ]);


        if (existH5Game) {
            await h5GameRepo.updateH5GameWithConditions({
                total_real_play: existH5Game.total_real_play + 1
            }, {
                id: existH5Game.id,
            });

            if (existUserH5GameReports) {
                if (existUserH5GameReports.length >= 0) {
                    const userH5GameReports = existUserH5GameReports
                        .filter(item => item.code !== lowerCode)
                        .slice(0, 3)
                    const deleteUserH5GameReportCodes = existUserH5GameReports
                        .filter(item => !userH5GameReports.includes(item.code))
                        .map(item => item.code);

                    if (deleteUserH5GameReportCodes.length) {
                        await userGameH5ReportRepo.deleteUserH5GameReport({
                            fid,
                            uid,
                            code: {
                                [Op.in]: deleteUserH5GameReportCodes
                            },
                        });
                    }
                }
                await userGameH5ReportRepo.createUserH5GameReport({
                    code: lowerCode,
                    fid,
                    uid,
                    created_at: currentTime,
                });
            }
        }

        if (existGameReport) {
            await gameReportRepo.updateGameReport(existGameReport, {
                total_open: existGameReport.total_open + 1,
            });
        } else {
            await gameReportRepo.createGameReport({
                code: lowerCode,
                fid,
                month,
                year,
                total_open: 1,
            });
        }

        if (existUserGameReport) {
            let games = existUserGameReport.games ? JSON.parse(existUserGameReport.games) : [];
            if (games[0] !== lowerCode) {
                games = [lowerCode, ...games.filter(game => ![lowerCode, upperCode].includes(game))].slice(0, 6);

                await userGameReportRepo.updateUserGameReport(existUserGameReport, {
                    games: JSON.stringify(games),
                });
            }
        } else {
            await userGameReportRepo.createUserGameReport({
                fid,
                uid,
                games: JSON.stringify([lowerCode]),
            });
        }
    }

    static async getListRss(req) {
        validateInput(req)

        const rssLink = req.query.link;

        const feed = await parser.parseURL(rssLink);

        const articles = feed.items.map(item => {
            const content = item.content || item.description;
            const title = item.title;
            const pubDate = item.pubDate;

            const imageRegex = /<img[^>]+src="([^">]+)"/;
            const linkRegex = /<a href=['"]([^'"]+)['"]/;

            const imageMatch = content.match(imageRegex);
            const linkMatch = content.match(linkRegex);

            const thumbnail = imageMatch ? imageMatch[1] : item.enclosure?.url;
            const link = linkMatch ? linkMatch[1] : item.link;

            return {
                title: title,
                pubDate: pubDate,
                link: link,
                thumbnail: thumbnail,
            };
        });

        articles.sort((a, b) => Date(b.pubDate) - Date(a.pubDate));

        return articles
    }
}

module.exports = GameService