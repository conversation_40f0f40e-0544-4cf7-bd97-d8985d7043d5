"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const gameReportModel = sequelize.define('game_report', {
    code: {
        type: DataTypes.STRING,
        allowNull: false,
        primaryKey: true,
    },
    fid: {
        type: DataTypes.STRING,
        allowNull: false,
        primaryKey: true,
    },
    month: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    year: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    total_open: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    timestamps: false,
    underscored: true,
    indexes: [
        {
            fields: ['code', 'fid', 'month', 'year'],
        },
    ],
    tableName: 'game_report'
})

module.exports = gameReportModel