"use strict";

const adminModel = require("../models/admin.model");

class AdminRepo {
    static async getListAdminsPagination(filter, include, attributes, order, page, limit) {
        const {count, rows} = await adminModel.findAndCountAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            offset: (page - 1) * limit,
            order: order,
            distinct: true
        });
        return {
            total_items: count,
            items_per_page: limit,
            total_pages: Math.ceil(count / limit),
            current_page: page,
            items: rows
        };
    }

    static async getAdminDetail(filter, include = null, attributes = []) {
        return await adminModel.findOne({
            where: filter,
            include: include,
            attributes: attributes
        })
    }

    static async createAdmin(data) {
        const result = await adminModel.create(data);
        return result.id;
    }

    static async updateAdmin(model, data) {
        await model.update(data);
    }
}

module.exports = AdminRepo;