"use strict";

const userH5GameReportModel = require("../models/userH5GameReport.model");

class UserGameReportRepo {
    static async getListUserH5GameReports(filter, include, attributes, order, limit = null) {
        return await userH5GameReportModel.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            order: order,
            limit: limit,
        })
    }

    static async getUserH5GameReportDetail(filter, attributes) {
        return await userH5GameReportModel.findOne({
            where: filter,
            attributes: attributes
        })
    }

    static async createUserH5GameReport(data) {
        return userH5GameReportModel.create(data);
    }

    static async updateUserH5GameReport(model, data) {
        await model.update(data);
    }

    static async deleteUserH5GameReport(filter) {
        await userH5GameReportModel.destroy({
            where: filter
        });
    }
}

module.exports = UserGameReportRepo;