"use strict";

const {AuthFailureError} = require("../../utils/errorResponse");

const verifyBullDashboard = async (req, res, next) => {
    try {
        const bullDashboardKey = req.cookies?.bull_dashboard_key || null

        if (!bullDashboardKey || bullDashboardKey !== 'oegjsc@1235') return next(new AuthFailureError());

        next()
    } catch (error) {
        next(new AuthFailureError(error.message));
    }
}

module.exports = {
    verifyBullDashboard
}