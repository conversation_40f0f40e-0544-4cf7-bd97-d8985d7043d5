"use strict";

const moment = require("moment-timezone");
const locationRepo = require("../../../repositories/location.repo");
const {defaultStatus} = require("../../../enums/objStatus");
const {updateLogEventQueue} = require("../../../queues/updateLogEvent.queue");
const redisService = require("../../redis.service");
const {locationTypes, itemReportTypes, OEGDefaultSettings, OtherDefaultSettings} = require("../../../enums");
const {Op} = require("sequelize");
const {updateLocationQueue} = require("../../../queues/updateLocation.queue");
const settingRepo = require("../../../repositories/setting.repo");

class LocationService {
    static async getListLocations(req) {
        return await locationRepo.getListLocations({
                status: defaultStatus.ACTIVE,
            },
            null,
            ['fid', 'name', 'type'],
            [['fid', 'ASC']],
            null);
    }

    static async getLocationDetail(req) {
        const {fid, fuid, uid, pcn, version} = req.query
        let result

        updateLocationQueue.add({
            fid,
            fuid,
        }, {
            removeOnComplete: true,
        });

        if (version && pcn && (fid?.startsWith('FU') || (!fid?.startsWith('FU') && uid))) {
            updateLogEventQueue.add({
                fid: fid ?? fuid ?? 0,
                uid,
                pcn,
                version
            }, {
                removeOnComplete: true,
            });
        }

        const redisKey = `cache:locations:${fid}`
        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        const location = await locationRepo.getLocationDetail({
                fid: fid ?? fuid ?? 0,
                status: {
                    [Op.in]: [defaultStatus.ACTIVE, defaultStatus.INACTIVE]
                },
            },
            ['fid', 'name', 'type', 'latest_version', 'force_update', 'status']);

        if (!location) {
            result = {
                fid: fid ?? fuid ?? 0,
                name: fid ?? fuid,
                region_id: 4,
                city_id: 35,
                type: locationTypes.OTHER,
                last_version: null,
                force_update: false
            }
        } else if (location.status === defaultStatus.ACTIVE) {
            result = location
        } else if (location.status === defaultStatus.INACTIVE) {
            result = null
        }

        await redisService.pushKeyToRedis(redisKey, JSON.stringify(result), 86400) // 1 ngày

        return result;
    }

    static async updateLocation(fid, fuid) {
        const currentTime = moment().tz('Asia/Ho_Chi_Minh').format('YYYY-MM-DD HH:mm:ss')

        const location = await locationRepo.getLocationDetail({
                fid: fid ?? fuid ?? 0,
                status: {
                    [Op.in]: [defaultStatus.ACTIVE, defaultStatus.INACTIVE]
                },
            },
            ['id', 'fid', 'fuid']);

        if (!location) {
            const newLocation = {
                fid: fid ?? fuid ?? 0,
                fuid,
                name: fid ?? fuid,
                region_id: 4,
                city_id: 35,
                force_update: false,
                status: defaultStatus.ACTIVE,
                type: locationTypes.OTHER,
                last_active_time: currentTime,
                created_at: currentTime,
            }

            await locationRepo.createLocation(newLocation)
        } else if (!location.fuid && fuid) {
            await locationRepo.updateLocation(location, {
                fuid,
                updated_at:currentTime,
            })
        }

        const settings = await settingRepo.getListSettings({
                status: defaultStatus.ACTIVE,
                fid: fid ?? fuid ?? 0
            },
            ['name', 'key', 'value'],
            [['key', 'ASC']]
        )

        if (settings.length < 13) {
            if (location) {
                await this.ensureMissingSettingsOnly(fid ?? fuid ?? 0, location.type)
            } else {
                await this.ensureMissingSettingsOnly(fid ?? fuid ?? 0, locationTypes.OTHER)
            }
        }
    }

    static async ensureMissingSettingsOnly(fid, type) {
        const default1Settings = {
            fb_icon: 'https://statics.oeg.vn/storage/FUS_APP_IMAGES/logo_facebook.webp',
            fb_link: 'https://www.facebook.com/',
            home_page_title: 'Experience Gaming Heaven',
            ins_icon: 'https://statics.oeg.vn/storage/FUS_APP_IMAGES/logo_instagram.webp',
            ins_link: 'https://www.instagram.com/',
            logo: null,
            tiktok_icon: 'https://statics.oeg.vn/storage/FUS_APP_IMAGES/logo_tiktok.webp',
            tiktok_link: 'https://www.tiktok.com/',
            total_news: 30,
            website_icon: 'https://statics.oeg.vn/storage/FUS_APP_IMAGES/logo_global.webp',
            website_link: 'https://google.com/',
            app_sound: true,
            go_to_game_menu: false,
        };

        const default2Settings = {
            fb_icon: null,
            fb_link: null,
            home_page_title: 'For Gamer, by Gamer',
            ins_icon: null,
            ins_link: null,
            logo: null,
            tiktok_icon: null,
            tiktok_link: null,
            total_news: 30,
            website_icon: null,
            website_link: null,
            app_sound: true,
            go_to_game_menu: false,
        };

        const defaultSettings = type === 1 ? default1Settings : default2Settings

        const existingSettings = await settingRepo.getListSettings({
                fid,
                status: defaultStatus.ACTIVE
            },
            ['key']
        );
        const existingKeys = existingSettings.map(s => s.key);

        const missingInserts = [];

        for (const [key, value] of Object.entries(defaultSettings)) {
            if (!existingKeys.includes(key)) {
                missingInserts.push({
                    fid,
                    key,
                    name: this.convertKeyToName(key),
                    type: 1,
                    value,
                    group: key === 'total_news' ? 2 : 1,
                    status: defaultStatus.ACTIVE,
                });
            }
        }

        if (missingInserts.length > 0) {
            await settingRepo.createSettingBulk(missingInserts);
        }

        // Xoá cache
        const key = `cache:settings:${fid}`;
        await redisService.deleteKeyFromRedis(key);
    }

    static convertKeyToName(key) {
        const map = {
            fb_icon: 'Icon 1',
            fb_link: 'Icon 1 link',
            home_page_title: 'Slogan',
            ins_icon: 'Icon 2',
            ins_link: 'Icon 2 link',
            logo: 'Logo',
            tiktok_icon: 'Icon 3',
            tiktok_link: 'Icon 3 link',
            total_news: 'Total news',
            website_icon: 'Icon 4 icon',
            website_link: 'Icon 4 link',
            app_sound: 'App sound',
            go_to_game_menu: 'Go to game menu',
        };
        return map[key] || key;
    }
}

module.exports = LocationService;