"use strict";

const {literal} = require("sequelize");
const fusGameRepo = require("../../../repositories/fusGame.repo");
const redisService = require("../../redis.service");

class FusGameService {
    static async getFusGameDetail(req) {
        const fid = req.params.fid || 0

        const redisKey = `cache:fus_games:${fid}`
        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        const result = await fusGameRepo.getFusGameDetail({
                fid,
            },
            ['fid', [
                literal(`CAST(fus_games.data AS JSON)`),
                'data'
            ]]
        )
        if (!result) {
            // throw new NotFoundError('Not found');
            return null;
        }

        await redisService.pushKeyToRedis(redisKey, JSON.stringify(result.data), 86400) // 1 ngày

        return result.data;
    }
}

module.exports = FusGameService;