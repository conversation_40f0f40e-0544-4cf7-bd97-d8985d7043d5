"use strict";

const {OK} = require("../../../utils/successResponse");
const playlistService = require("../../../services/website/v1/playlist.service");

class PlaylistController {
    getListPlaylist = async (req, res, next) => {
        new OK({
            message: 'Get list playlist successfully.',
            metadata: await playlistService.getListPlaylist(req)
        }).send(res)
    }
}

module.exports = new PlaylistController();