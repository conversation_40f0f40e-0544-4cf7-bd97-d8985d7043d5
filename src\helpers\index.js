const {validationResult} = require('express-validator');
const moment = require('moment-timezone');
const fs = require('fs');
const path = require('path');
const {BadRequestError, NotFoundError} = require("../utils/errorResponse");

const asyncHandler = fn => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    }
}

function asyncVersionHandler(controllerName, methodName, controllerPath = 'src/controllers/website') {
    return (req, res, next) => {
        try {
            const controller = getController(controllerName, controllerPath, req.api_version);
            return asyncHandler(controller[methodName])(req, res, next);
        } catch (err) {
            next(err);
        }
    };
}

const getController = (name, dir, version) => {
    const controllerPath = path.join(process.cwd(), dir, `v${version}`, `${name}.controller.js`);
    if (!fs.existsSync(controllerPath)) {
        throw new NotFoundError();
    }
    console.log("controllerPath", controllerPath)
    return require(controllerPath);
}

const validateInput = (req) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        const formatErrors = errors.array().reduce((acc, err) => {
            acc[err.path] = err.msg;
            return acc;
        }, {});
        throw new BadRequestError(formatErrors);
        // throw new BadRequestError(errors.errors[0].msg);
    }
}

const handleAPI = async (promise) => {
    try {
        return await promise();
    } catch (error) {
        if (error.response) {
            const newError = new Error(error.response.data.message);
            newError.status = error.response.data.code;
            if (error.response.data.errors) {
                newError.errors = error.response.data.errors;
            }

            throw newError;
        }
        throw error;
    }
};

function isDateTimeString(value, format) {
    return moment(value, format, true).isValid();
}

module.exports = {
    asyncHandler,
    asyncVersionHandler,
    getController,
    validateInput,
    handleAPI,
    isDateTimeString
}