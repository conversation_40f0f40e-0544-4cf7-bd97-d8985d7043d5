"use strict";

const Queue = require('bull');

const updateLogEventQueue = new Queue('updateLogEventQueue', {
    prefix: `${process.env.REDIS_KEY_PREFIX}bull`,
    redis: {
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT,
        password: process.env.REDIS_PASSWORD,
    },
});

updateLogEventQueue.process(async (job, done) => {
    const {fid, uid, pcn, version} = job.data;

    try {
        await require('../services/website/v1/tracking.service').logEvent(fid, uid, pcn, version);
        done();
    } catch (error) {
        console.error(`Failed to update log event ${fid}/${uid}/${pcn}/${version}::`, error.message);
        done(new Error(`Failed to update log event ${fid}/${uid}/${pcn}/${version}:: ` + error.message));
    }
});

// Logging và xử lý sự kiện
// sendEmailQueue.on('completed', (job) => {
//     console.log(`Job ${job.id} has been completed`);
// });

updateLogEventQueue.on('failed', (job, err) => {
    console.error(`Job ${job.id} failed with error: ${err.message}`);
});

updateLogEventQueue.on('stalled', (job) => {
    console.warn(`Job ${job.id} has stalled and will be reprocessed`);
});

// sendEmailQueue.on('delayed', (job) => {
//     console.log(`Job ${job.id} has been delayed`);
// });

// Xóa toàn bộ queue và khởi động lại
// async function obliterateQueue() {
//     await updateLogEventQueue.obliterate({ force: true });
//     console.log("Queue has been obliterated!");
// }

module.exports = {
    updateLogEventQueue,
};