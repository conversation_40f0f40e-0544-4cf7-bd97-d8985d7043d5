"use strict";

const {OK} = require("../../../utils/successResponse");
const videoService = require("../../../services/website/v1/video.service");

class VideoController {
    getListLatestVideos = async (req, res, next) => {
        new OK({
            message: 'Get list latest videos successfully.',
            metadata: await videoService.getListLatestVideos(req)
        }).send(res)
    }

    getListTopViewVideos = async (req, res, next) => {
        new OK({
            message: 'Get list top view videos successfully.',
            metadata: await videoService.getListTopViewVideos(req)
        }).send(res)
    }
}

module.exports = new VideoController();