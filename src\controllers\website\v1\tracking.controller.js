"use strict";

const {OK} = require("../../../utils/successResponse");
const trackingService = require("../../../services/website/v1/tracking.service");

class TrackingController {
    openLink = async (req, res, next) => {
        new OK({
            message: 'Open link successfully.',
            metadata: await trackingService.openLink(req)
        }).send(res)
    }

    openBanner = async (req, res, next) => {
        new OK({
            message: 'Open banner successfully.',
            metadata: await trackingService.openBanner(req)
        }).send(res)
    }

    openVideo = async (req, res, next) => {
        new OK({
            message: 'Open video successfully.',
            metadata: await trackingService.openVideo(req)
        }).send(res)
    }
}

module.exports = new TrackingController();