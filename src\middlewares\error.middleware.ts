import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest, CustomError } from '@/types/common';
import { ApiResponse } from '@/utils/response';

/**
 * 404 Not Found middleware
 */
export const notFound = (req: Request, res: Response, next: NextFunction): void => {
  const error = new Error(`Not Found - ${req.originalUrl}`) as CustomError;
  error.status = 404;
  next(error);
};

/**
 * Global error handler middleware
 */
export const errorHandler = async (
  error: CustomError,
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  // Log error details
  console.error('Error Stack:', error.stack);
  
  const statusCode = error.status || 500;
  const message = error.message || 'Internal Server Error';
  
  // Calculate request duration if available
  const duration = req.now ? Date.now() - req.now : 0;
  const logMessage = `${statusCode}::${duration}ms::${message}`;
  
  // In real implementation, log to external service
  console.error('Request Error:', logMessage);
  
  // Prepare error response
  const response = {
    status: 'error' as const,
    code: statusCode,
    message,
    ...(error.errors && { errors: error.errors }),
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack }),
  };

  // Send error response
  res.status(statusCode).json(response);
};

/**
 * Async error wrapper for route handlers
 */
export const asyncErrorHandler = (
  fn: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<any>
) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Custom error classes
 */
export class AppError extends Error {
  public status: number;
  public errors?: Record<string, string>;
  public isOperational: boolean;

  constructor(
    message: string,
    status: number = 500,
    errors?: Record<string, string>,
    isOperational: boolean = true
  ) {
    super(message);
    this.status = status;
    this.errors = errors;
    this.isOperational = isOperational;
    this.name = this.constructor.name;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(errors: Record<string, string>, message: string = 'Validation Error') {
    super(message, 400, errors);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication Failed') {
    super(message, 401);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Access Denied') {
    super(message, 403);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource Not Found') {
    super(message, 404);
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource Conflict') {
    super(message, 409);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Too Many Requests') {
    super(message, 429);
  }
}

/**
 * Handle unhandled promise rejections
 */
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // In production, you might want to exit the process
  // process.exit(1);
});

/**
 * Handle uncaught exceptions
 */
process.on('uncaughtException', (error: Error) => {
  console.error('Uncaught Exception:', error);
  // In production, you should exit the process
  // process.exit(1);
});
