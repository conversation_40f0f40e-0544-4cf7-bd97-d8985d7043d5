import { Request, Response, NextFunction } from 'express';
import { ZodSchema, ZodError } from 'zod';
import { AuthenticatedRequest } from '@/types/common';
import { ValidationError } from './error.middleware';

/**
 * Validation target types
 */
type ValidationTarget = 'body' | 'query' | 'params' | 'headers';

/**
 * Validation options
 */
interface ValidationOptions {
  body?: ZodSchema;
  query?: ZodSchema;
  params?: ZodSchema;
  headers?: ZodSchema;
  stripUnknown?: boolean;
}

/**
 * Generic Zod validation middleware
 */
export const validate = (options: ValidationOptions) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      const errors: Record<string, string> = {};

      // Validate body
      if (options.body) {
        try {
          req.body = options.body.parse(req.body);
        } catch (error) {
          if (error instanceof ZodError) {
            error.errors.forEach(err => {
              const path = err.path.join('.');
              errors[`body.${path}`] = err.message;
            });
          }
        }
      }

      // Validate query parameters
      if (options.query) {
        try {
          req.query = options.query.parse(req.query);
        } catch (error) {
          if (error instanceof ZodError) {
            error.errors.forEach(err => {
              const path = err.path.join('.');
              errors[`query.${path}`] = err.message;
            });
          }
        }
      }

      // Validate path parameters
      if (options.params) {
        try {
          req.params = options.params.parse(req.params);
        } catch (error) {
          if (error instanceof ZodError) {
            error.errors.forEach(err => {
              const path = err.path.join('.');
              errors[`params.${path}`] = err.message;
            });
          }
        }
      }

      // Validate headers
      if (options.headers) {
        try {
          req.headers = { ...req.headers, ...options.headers.parse(req.headers) };
        } catch (error) {
          if (error instanceof ZodError) {
            error.errors.forEach(err => {
              const path = err.path.join('.');
              errors[`headers.${path}`] = err.message;
            });
          }
        }
      }

      // If there are validation errors, throw ValidationError
      if (Object.keys(errors).length > 0) {
        throw new ValidationError(errors, 'Validation failed');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Validate request body only
 */
export const validateBody = (schema: ZodSchema) => {
  return validate({ body: schema });
};

/**
 * Validate query parameters only
 */
export const validateQuery = (schema: ZodSchema) => {
  return validate({ query: schema });
};

/**
 * Validate path parameters only
 */
export const validateParams = (schema: ZodSchema) => {
  return validate({ params: schema });
};

/**
 * Validate headers only
 */
export const validateHeaders = (schema: ZodSchema) => {
  return validate({ headers: schema });
};

/**
 * Create validation middleware for specific target
 */
export const createValidator = (target: ValidationTarget, schema: ZodSchema) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      const dataToValidate = req[target];
      const validatedData = schema.parse(dataToValidate);
      
      // Replace the original data with validated data
      (req as any)[target] = validatedData;
      
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const errors: Record<string, string> = {};
        error.errors.forEach(err => {
          const path = err.path.join('.');
          errors[`${target}.${path}`] = err.message;
        });
        next(new ValidationError(errors, `${target} validation failed`));
      } else {
        next(error);
      }
    }
  };
};

/**
 * Optional validation middleware - doesn't fail if validation fails
 */
export const optionalValidate = (options: ValidationOptions) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      // Validate body
      if (options.body) {
        try {
          req.body = options.body.parse(req.body);
        } catch (error) {
          // Silently ignore validation errors for optional validation
        }
      }

      // Validate query parameters
      if (options.query) {
        try {
          req.query = options.query.parse(req.query);
        } catch (error) {
          // Silently ignore validation errors for optional validation
        }
      }

      // Validate path parameters
      if (options.params) {
        try {
          req.params = options.params.parse(req.params);
        } catch (error) {
          // Silently ignore validation errors for optional validation
        }
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Sanitize input middleware - removes potentially dangerous content
 */
export const sanitizeInput = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  const sanitizeObject = (obj: any): any => {
    if (typeof obj === 'string') {
      // Basic XSS protection
      return obj
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '');
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitizeObject(value);
      }
      return sanitized;
    }
    
    return obj;
  };

  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  
  if (req.query) {
    req.query = sanitizeObject(req.query);
  }

  next();
};
