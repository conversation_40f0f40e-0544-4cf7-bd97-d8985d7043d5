"use strict";

const h5GameCategoryModel = require("../models/h5GameCategory.model");

class H5GameCategoryRepo {
    static async getListH5GameCategories(filter, attributes, order, limit) {
        return await h5GameCategoryModel.findAll({
            where: filter,
            limit: limit,
            order: order,
            attributes: attributes
        })
    }

    static async getListH5GameCategoriesPagination(filter, attributes, order, page, limit) {
        const {count, rows} = await h5GameCategoryModel.findAndCountAll({
            where: filter,
            attributes: attributes,
            order: order,
            offset: (page - 1) * limit,
            limit: limit
        })

        return {
            total_items: count,
            items_per_page: limit,
            current_page: page,
            total_pages: Math.ceil(count / limit),
            items: rows
        };
    }

    static async getH5GameCategoryDetail(filter, include, attributes) {
        return await h5GameCategoryModel.findOne({
            where: filter,
            include: include,
            attributes: attributes
        })
    }

    static async createH5GameCategory(data) {
        const result = await h5GameCategoryModel.create(data);
        return result.id;
    }

    static async updateH5GameCategory(model, data, filter = {}) {
        await model.update(data, {
            where: filter
        });
    }

    static async updateH5GameCategoryWithConditions(data, filter = {}) {
        await h5GameCategoryModel.update(data, {
            where: filter
        });
    }
}

module.exports = H5GameCategoryRepo;