"use strict";

const eventModel = require("../models/event.model");

class EventRepo {
    static async getListEvents(filter, attributes, order, limit) {
        return await eventModel.findAll({
            where: filter,
            limit: limit,
            order: order,
            attributes: attributes
        })
    }

    static async getListEventsPagination(filter, include, attributes, order, page, limit) {
        const {count, rows} = await eventModel.findAndCountAll({
            where: filter,
            include: include,
            attributes: attributes,
            order: order,
            offset: (page - 1) * limit,
            limit: limit
        })

        return {
            total_items: count,
            items_per_page: limit,
            current_page: page,
            total_pages: Math.ceil(count / limit),
            items: rows
        };
    }

    static async getEventDetail(filter, include, attributes) {
        return await eventModel.findOne({
            where: filter,
            include: include,
            attributes: attributes
        })
    }

    static async createEvent(data) {
        const result = await eventModel.create(data);
        return result.id;
    }

    static async updateEvent(model, data, filter = {}) {
        await model.update(data, {
            where: filter
        });
    }

    static async updateEventWithConditions(data, filter = {}) {
        await eventModel.update(data, {
            where: filter
        });
    }
}

module.exports = EventRepo;