"use strict";

const redisService = require('../services/redis.service');
const {v4: uuidV4} = require('uuid');
const moment = require("moment-timezone");

class RedisLogger {
    constructor() {
        this.expireTime = 30 * 24 * 60 * 60; // Thời gian hết hạn là 30 ngày
    }

    formatLogEntry(req, level = 'info', message = null, errors = null) {
        const currentTime = moment().tz('Asia/Ho_Chi_Minh').format('YYYY-MM-DD HH:mm:ss');
        const logLevel = `${process.env.NODE_ENV}.${level.toUpperCase()}`;
        const ip = req.headers['x-forwarded-for'] || req.ip;
        const method = req.method;
        const url = req.originalUrl;
        const requestId = req.requestId || Date.now();
        const logMsg = message;

        let payload = '';
        if (['POST', 'PUT'].includes(method)) {
            try {
                if (req.body && typeof req.body === 'object') {
                    payload = `::Payload=${JSON.stringify(req.body)}`;
                }
            } catch (err) {
                payload = '::Payload=parse_error';
            }
        }

        return `[${currentTime}] ${logLevel} [${ip}][${requestId}]${req.admin?.id ? `[AD${req.admin.id}]` : ''} [${method}]"${url}"${logMsg ? `::${logMsg}` : ''}${errors ? `::${JSON.stringify(errors)}` : ''}${payload}`;
    }

    getLogKey(type) {
        const currentDate = moment().tz('Asia/Ho_Chi_Minh').format('YYYY-MM-DD');
        return `log:${type}:${currentDate}`;
    }

    getExpiredTime() {
        const now = moment().tz('Asia/Ho_Chi_Minh');
        const nextMonthEnd = moment().tz('Asia/Ho_Chi_Minh').add(3, 'months').endOf('month');
        // Tính bằng giây
        return nextMonthEnd.diff(now, 'seconds');
    }

    async log(req, type, message = null, errors = null) {
        const logEntry = this.formatLogEntry(req, type, message, errors);
        const key = this.getLogKey(type);

        const existingLogs = await redisService.getKey(key);
        const newLogs = existingLogs ? `${logEntry}\n${existingLogs}` : logEntry;

        const expireTime = this.getExpiredTime()

        await redisService.pushKeyToRedis(key, newLogs, expireTime);
    }

    async info(req) {
        await this.log(req, 'info')
    }

    async error(req, message, errors = null) {
        // const logEntry = this.formatLogEntry(req, 'error', message, errors);
        // const key = this.getLogKey('info');
        // const existingLogs = await redisService.getKey(key);
        // const newLogs = existingLogs ? `${logEntry}\n${existingLogs}` : logEntry;
        // const expireTime = this.getExpiredTime()
        // await redisService.pushKeyToRedis(key, newLogs, expireTime);

        await this.log(req, 'error', message, errors)
    }
}

module.exports = new RedisLogger();