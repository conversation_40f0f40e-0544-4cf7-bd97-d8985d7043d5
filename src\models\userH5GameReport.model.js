"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const userH5GameReportModel = sequelize.define('user_h5_game_report', {
    fid: {
        type: DataTypes.STRING,
        allowNull: false,
        primaryKey: true,
    },
    uid: {
        type: DataTypes.STRING,
        allowNull: false,
        primaryKey: true,
    },
    code: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
    },
}, {
    timestamps: false,
    underscored: true,
    indexes: [
        {
            fields: ['fid', 'uid'],
        },
    ],
    tableName: 'user_h5_game_report'
})

module.exports = userH5GameReportModel