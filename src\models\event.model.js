"use strict";

const {DataTypes} = require('sequelize');
const {logDB} = require('../dbs/mysql.db');

const eventModel = logDB.define('events', {
    id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
    },
    fid: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    uid: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    pcname: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    version: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: logDB.literal('CURRENT_TIMESTAMP')
    },
}, {
    timestamps: false,
    underscored: true,
    tableName: 'events'
});


module.exports = eventModel;