"use strict";

const gameReportModel = require("../models/gameReport.model");

class GameReportRepo {
    static async getListGameReports(filter, include, attributes, group, order, limit) {
        return await gameReportModel.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            group: group,
            order: order,
            limit: limit,
        })
    }

    static async getGameReportDetail(filter, attributes) {
        return await gameReportModel.findOne({
            where: filter,
            attributes: attributes
        })
    }

    static async createGameReport(data) {
        return gameReportModel.create(data);
    }

    static async updateGameReport(model, data) {
        await model.update(data);
    }
}

module.exports = GameReportRepo;