"use strict";

const express = require('express');
const {asyncHand<PERSON>} = require("../../helpers");
const trackingController = require("../../controllers/website/v1/tracking.controller");

const router = express.Router();

router.post('/link', [async<PERSON>and<PERSON>(trackingController.openLink)]);
router.post('/banner', [async<PERSON>and<PERSON>(trackingController.openBanner)]);
router.post('/video', [asyncHandler(trackingController.openVideo)]);

module.exports = router;