{"name": "fus-cms-backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@bull-board/api": "^6.0.0", "@bull-board/express": "^6.0.0", "axios": "^1.7.7", "bcrypt": "^5.1.1", "bull": "^4.16.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.0", "express-rate-limit": "^7.4.0", "express-validator": "^7.2.0", "helmet": "^7.1.0", "ioredis": "^5.4.1", "moment-timezone": "^0.5.45", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.3", "node-telegram-bot-api": "^0.66.0", "rate-limit-redis": "^4.2.0", "rss-parser": "^3.13.0", "sequelize": "^6.37.3", "uuid": "^10.0.0"}}