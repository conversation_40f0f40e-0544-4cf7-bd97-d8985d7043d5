const {TELEGRAM_BOT_TOKEN, TEST_CHAT_ID} = process.env;
const TelegramBot = require('node-telegram-bot-api');

const bot = new TelegramBot(TELEGRAM_BOT_TOKEN, {polling: false, request: {
        agentOptions: {
            family: 4
        }
    }});

// bot.on('message', (msg) => {
//     console.log(msg)
//     const chatId = msg.chat.id;
//     console.log("chatId",chatId)
// });

const sendMessage = (message, chatID = TEST_CHAT_ID) => {
    if (process.env.NODE_ENV !== 'production') {
        message = `[[${process.env.NODE_ENV.toUpperCase()}]] ${message}`
    }

    bot.sendMessage(chatID, message, {parse_mode: 'Markdown'})
}

module.exports = {
    sendMessage
}
