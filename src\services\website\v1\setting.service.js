"use strict";

const {Op} = require("sequelize");
const settingRepo = require("../../../repositories/setting.repo");
const {defaultStatus} = require("../../../enums/objStatus");
const redisService = require("../../redis.service");
const locationRepo = require("../../../repositories/location.repo");
const {OEGDefaultSettings, OtherDefaultSettings, locationTypes} = require("../../../enums");

class SettingService {
    static async getListSettings(req) {
        const fid = req.params.fid ?? 0

        const redisKey = `cache:settings:${fid}`

        const cachedData = await redisService.getKey(redisKey);
        if (cachedData && JSON.parse(cachedData).length) {
            return JSON.parse(cachedData);
        }

        let result = await settingRepo.getListSettings({
                status: defaultStatus.ACTIVE,
                fid
            },
            ['name', 'key', 'value'],
            [['key', 'ASC']]
        )

        if (result.length === 0) {
            let locationType = locationTypes.OTHER

            const location = await locationRepo.getLocationDetail({
                    fid,
                    status: {
                        [Op.in]: [defaultStatus.ACTIVE, defaultStatus.INACTIVE]
                    },
                    deleted_at: null,
                    deleted_by: null
                },
                ['type']);
            if (location) {
                locationType = location.type;
            }

            if (locationType === locationTypes.OEG) {
                result = OEGDefaultSettings
            } else {
                result = OtherDefaultSettings
            }
        }

        await redisService.pushKeyToRedis(redisKey, JSON.stringify(result), 86400) // 1 ngày

        return result
    }
}

module.exports = SettingService