"use strict";

const videoModel = require("../models/video.model");

class VideoRepo {
    static async getListVideos(filter, include, attributes, order, limit) {
        return await videoModel.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            order: order
        });
    }

    static async getListVideosPagination(filter, include, attributes, order, page, limit) {
        const {count, rows} = await videoModel.findAndCountAll({
            where: filter,
            include: include,
            distinct: true,
            attributes: attributes,
            limit: limit,
            offset: (page - 1) * limit,
            order: order
        });
        return {
            total_items: count,
            items_per_page: limit,
            total_pages: Math.ceil(count / limit),
            current_page: page,
            items: rows
        };
    }

    static async getVideoDetail(filter, include, attributes) {
        return await videoModel.findOne({
            where: filter,
            include: include,
            attributes: attributes,
        });
    }

    static async createVideo(data) {
        const result = await videoModel.create(data);
        return result.id;
    }

    static async updateVideo(model, data) {
        await model.update(data);
    }
}

module.exports = VideoRepo;