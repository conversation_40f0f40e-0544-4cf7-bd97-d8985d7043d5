"use strict";

const playlistRepo = require("../../../repositories/playlist.repo");
const videoRepo = require("../../../repositories/video.repo");
const {videoStatus, booleanStatus, defaultStatus} = require("../../../enums/objStatus");
const {literal, Op, fn, col} = require("sequelize");
const redisService = require("../../redis.service");
const bannerReportRepo = require("../../../repositories/bannerReport.repo");
const videoModel = require("../../../models/video.model");
const {itemReportTypes} = require("../../../enums");

class VideoService {
    static async getListLatestVideos(req) {
        const {playlist_slug, search, offset, limit} = req.query

        const redisKey = `cache:video:${playlist_slug}:${limit}:${offset}:${search}`

        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        let playlistCondition = {}
        if (playlist_slug) {
            const playlistSlugArr = playlist_slug ? playlist_slug.split(",").map(item => item.trim()) : []

            let playlist = await playlistRepo.getListPlaylists({
                    slug: playlistSlugArr,
                    status: defaultStatus.ACTIVE,
                    deleted_at: null,
                    deleted_by: null
                },
                null,
                ['id', 'name', 'icon', 'slug']
            );
            if (!playlist.length) {
                return []
            } else {
                playlistCondition = {
                    id: {
                        [Op.in]: literal(`(SELECT video_id FROM playlist_video WHERE playlist_id IN (${playlist.map(item => item.id)}))`)
                    },
                }
            }
        }

        let searchCondition = {}
        if (search) {
            searchCondition = {
                title: {
                    [Op.like]: `%${search}%`
                }
            }
        }

        let result = await videoRepo.getListVideosPagination({
                ...searchCondition,
                ...playlistCondition,
                status: videoStatus.ACTIVE,
                deleted_at: null,
                deleted_by: null
            },
            null,
            ['id', 'thumbnail', 'title', 'year', 'iframe_url'],
            [
                // ['publish_date', 'DESC'],
                ['created_at', 'DESC'],
                ['id', 'DESC']
            ],
            parseInt(Math.floor((offset || 0) / (limit || 15)) + 1),
            parseInt(limit || 15)
        )

        const data = {
            total_videos: result.total_items,
            // videos: result.items.sort(() => Math.random() - 0.5).slice(0, limit || 15)
            videos: result.items
        }

        await redisService.pushKeyToRedis(redisKey, JSON.stringify(data), search ? 60 : 86400) // 1 ngày

        return data
    }

    static async getListTopViewVideos(req) {
        const fid = req.query.fid || 0

        const redisKey = `cache:top_view_videos:${fid}`

        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }

        let fidCondition = {}
        if (fid) {
            fidCondition = {
                fid
            }
        }

        const topViewVideos = await bannerReportRepo.getListBannerReports(
            {
                type: itemReportTypes.VIDEO,
                ...fidCondition,
            },
            [{
                model: videoModel,
                attributes: ['id', 'thumbnail', 'title', 'year', 'iframe_url'],
                where: {
                    status: videoStatus.ACTIVE
                },
                required: true
            }],
            [
                'banner_id',
                'type',
                [fn('SUM', col('total_open')), 'total_open']
            ],
            ['banner_id', 'type'],
            [[fn('SUM', col('total_open')), 'DESC']],
            req.query.limit || 7
        );

        const result = topViewVideos.map(item => item.video).filter(video => video !== null)

        await redisService.pushKeyToRedis(redisKey, JSON.stringify(result), 1800) // 30p

        return result
    }
}

module.exports = VideoService;