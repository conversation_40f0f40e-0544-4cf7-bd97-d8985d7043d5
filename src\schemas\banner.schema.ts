import { z } from 'zod';
import { baseQuerySchema } from './common.schema';

// Banner query parameters schema
export const bannerQuerySchema = baseQuerySchema.extend({
  fid: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined),
  position: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined),
  limit: z.string().optional().transform(val => {
    const parsed = val ? parseInt(val, 10) : 10;
    return Math.min(Math.max(1, parsed), 50); // Limit between 1-50 for banners
  }),
});

// Banner response schema
export const bannerResponseSchema = z.object({
  id: z.number(),
  title: z.string(),
  image: z.string(),
  redirect_link: z.string().nullable(),
  position_id: z.number(),
  start_date: z.date(),
  end_date: z.date(),
  sort_order: z.number(),
  click_count: z.number(),
  status: z.number(),
  created_at: z.date(),
  updated_at: z.date(),
});

// Banner V2 response schema
export const bannerV2ResponseSchema = z.object({
  id: z.number(),
  image: z.string(),
  apply_date: z.date(),
  display_type: z.number(),
  destination_type: z.number(),
  active_click: z.boolean(),
  sort: z.number().nullable(),
  redirect_link: z.any(), // JSON field
  status: z.number(),
  created_at: z.date(),
  updated_at: z.date(),
});

// Banner list response schema
export const bannerListResponseSchema = z.object({
  banners: z.array(bannerResponseSchema),
  total: z.number(),
});

// Banner V2 list response schema
export const bannerV2ListResponseSchema = z.object({
  banners: z.array(bannerV2ResponseSchema),
  total: z.number(),
});

// Type exports
export type BannerQueryParams = z.infer<typeof bannerQuerySchema>;
export type BannerResponse = z.infer<typeof bannerResponseSchema>;
export type BannerV2Response = z.infer<typeof bannerV2ResponseSchema>;
export type BannerListResponse = z.infer<typeof bannerListResponseSchema>;
export type BannerV2ListResponse = z.infer<typeof bannerV2ListResponseSchema>;
