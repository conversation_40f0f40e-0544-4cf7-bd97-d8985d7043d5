stages:
  - build
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  SSH_KEY_PATH: "/root/.ssh/id_rsa"
  SSH_KNOWN_HOSTS: "/root/.ssh/known_hosts"
  SECURE_FILES_DOWNLOAD_PATH: 'secure_folder'
  SWARM_CONFIG_FILE: '/root/config-swarm/apifus.yml'
  STACK_NAME: 'apifus'
before_script:
  - apk update
  - apk add --no-cache curl bash
build_image:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - curl --silent "https://gitlab.com/gitlab-org/incubation-engineering/mobile-devops/download-secure-files/-/raw/main/installer" | bash
    - cp $SECURE_FILES_DOWNLOAD_PATH/.env .env
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build -t $CI_REGISTRY_IMAGE:latest .
    - docker push $CI_REGISTRY_IMAGE:latest
  only:
    - master
#   when: manual

deploy_to_swarm:
  stage: deploy
  image: docker:latest
  script:
    - curl --silent "https://gitlab.com/gitlab-org/incubation-engineering/mobile-devops/download-secure-files/-/raw/main/installer" | bash
    - chmod 600 $SECURE_FILES_DOWNLOAD_PATH/private_key
    - mkdir ~/.ssh/ >> /dev/null 2>&1
    - touch ~/.ssh/known_hosts >> /dev/null 2>&1
    - ssh-keyscan -H $SSH_HOST >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - ssh -tt -i $SECURE_FILES_DOWNLOAD_PATH/private_key $SSH_USER@$SSH_HOST "docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY && 
      docker stack rm $STACK_NAME && 
      docker stack deploy --with-registry-auth --resolve-image always -c  $SWARM_CONFIG_FILE $STACK_NAME"
  only:
    - master
  when: manual
