"use strict";

const apiVersionRepo = require('../repositories/apiVersion.repo');
const {BadRequestError, ForbiddenError} = require("../utils/errorResponse");
const redisService = require("../services/redis.service");

async function getApiVersion(req, res, next) {
    const clientVersion = req.headers['x-client-version']?.toString();

    // if (!clientVersion) {
    //     return next(new BadRequestError(null, "Missing client version"));
    // }

    if (clientVersion) {
        const redisKey = `cache:api_versions:${clientVersion}`
        const cachedData = await redisService.getKey(redisKey);
        if (cachedData) {
            req.api_version = cachedData
        } else {
            const apiVersion = await apiVersionRepo.getApiVersionDetail({
                client: clientVersion,
            }, ['api']);

            if (!apiVersion) {
                return next(new ForbiddenError("Invalid version"));
            }

            await redisService.pushKeyToRedis(redisKey, apiVersion.api, 86400) // 1 ngày

            req.api_version = apiVersion.api
        }
    } else {
        req.api_version = 1
    }

    next();
}

module.exports = {
    getApiVersion,
}