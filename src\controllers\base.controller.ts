import { Response } from 'express';
import { AuthenticatedRequest } from '@/types/common';
import { ApiResponse } from '@/utils/response';

/**
 * Base controller class with common functionality
 */
export abstract class BaseController {
  /**
   * Send success response
   */
  protected success<T>(
    res: Response,
    data: T,
    message: string = 'Success',
    code: number = 200
  ): void {
    ApiResponse.success(res, data, message, code);
  }

  /**
   * Send error response
   */
  protected error(
    res: Response,
    message: string = 'Internal Server Error',
    code: number = 500,
    errors?: Record<string, string>
  ): void {
    ApiResponse.error(res, message, code, errors);
  }

  /**
   * Send bad request response
   */
  protected badRequest(
    res: Response,
    message: string = 'Bad Request',
    errors?: Record<string, string>
  ): void {
    ApiResponse.badRequest(res, message, errors);
  }

  /**
   * Send not found response
   */
  protected notFound(
    res: Response,
    message: string = 'Not Found'
  ): void {
    ApiResponse.notFound(res, message);
  }

  /**
   * Send unauthorized response
   */
  protected unauthorized(
    res: Response,
    message: string = 'Unauthorized'
  ): void {
    ApiResponse.unauthorized(res, message);
  }

  /**
   * Send forbidden response
   */
  protected forbidden(
    res: Response,
    message: string = 'Forbidden'
  ): void {
    ApiResponse.forbidden(res, message);
  }

  /**
   * Extract pagination parameters from request
   */
  protected getPaginationParams(req: AuthenticatedRequest) {
    const page = Math.max(1, parseInt(req.query.page as string) || 1);
    const limit = Math.min(100, Math.max(1, parseInt(req.query.limit as string) || 20));
    const offset = (page - 1) * limit;

    return { page, limit, offset };
  }

  /**
   * Extract sort parameters from request
   */
  protected getSortParams(req: AuthenticatedRequest) {
    const sortBy = req.query.sortBy as string || 'created_at';
    const sortOrder = (req.query.sortOrder as string)?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    return { sortBy, sortOrder };
  }

  /**
   * Extract search parameter from request
   */
  protected getSearchParam(req: AuthenticatedRequest): string | undefined {
    return req.query.search as string || undefined;
  }

  /**
   * Build pagination response metadata
   */
  protected buildPaginationResponse<T>(
    items: T[],
    total: number,
    page: number,
    limit: number
  ) {
    const totalPages = Math.ceil(total / limit);
    
    return {
      items,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * Validate required parameters
   */
  protected validateRequired(
    params: Record<string, any>,
    requiredFields: string[]
  ): string[] {
    const missing: string[] = [];
    
    for (const field of requiredFields) {
      if (params[field] === undefined || params[field] === null || params[field] === '') {
        missing.push(field);
      }
    }
    
    return missing;
  }
}
