import { Response, NextFunction } from 'express';
import * as crypto from 'crypto';
import { AuthenticatedRequest } from '@/types/common';
import { AuthenticationError, AuthorizationError, ValidationError } from './error.middleware';

// Headers constants
const HEADERS = {
  X_TIMESTAMP: 'x-timestamp',
  X_SIGNATURE: 'x-signature',
  X_CLIENT_VERSION: 'x-client-version',
} as const;

/**
 * API Version middleware - determines API version from headers
 */
export const getApiVersion = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const clientVersion = req.headers[HEADERS.X_CLIENT_VERSION]?.toString();

    if (clientVersion) {
      // In real implementation, this would:
      // 1. Check Redis cache first
      // 2. Query database for API version mapping
      // 3. Cache the result
      
      // For now, simulate version mapping
      const versionMap: Record<string, number> = {
        '1.0.0': 1,
        '1.1.0': 1,
        '2.0.0': 2,
        '2.1.0': 2,
      };

      req.api_version = versionMap[clientVersion] || 1;
    } else {
      req.api_version = 1; // Default version
    }

    next();
  } catch (error) {
    next(new AuthenticationError('Invalid API version'));
  }
};

/**
 * Client signature verification middleware
 */
export const verifyClient = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const timestamp = req.headers[HEADERS.X_TIMESTAMP]?.toString();
    const clientSignature = req.headers[HEADERS.X_SIGNATURE]?.toString();

    if (!timestamp || !clientSignature) {
      throw new ValidationError(
        { timestamp: 'Missing timestamp', signature: 'Missing signature' },
        'Missing timestamp or signature'
      );
    }

    const currentTimestamp = Date.now();
    const requestTimestamp = parseInt(timestamp, 10);
    const MAX_TIME_DIFF = 5 * 60 * 1000; // 5 minutes

    // Check timestamp validity
    if (isNaN(requestTimestamp) || Math.abs(currentTimestamp - requestTimestamp) > MAX_TIME_DIFF) {
      throw new AuthenticationError('Request timestamp is invalid or expired');
    }

    // Generate server signature
    const payload = JSON.stringify(req.body || '');
    const secretKey = process.env.SIGNATURE_SECRET_KEY || 'default-secret-key';
    const serverSignature = generateSignature(secretKey, timestamp, payload);

    // Compare signatures using timing-safe comparison
    const clientSignatureBuffer = Buffer.from(clientSignature);
    const serverSignatureBuffer = Buffer.from(serverSignature);

    if (
      clientSignatureBuffer.length !== serverSignatureBuffer.length ||
      !crypto.timingSafeEqual(clientSignatureBuffer, serverSignatureBuffer)
    ) {
      throw new AuthenticationError('Invalid signature');
    }

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Enhanced client verification middleware (V2)
 */
export const verifyClientV2 = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const timestamp = Number(req.headers[HEADERS.X_TIMESTAMP]);
    const clientSignature = req.headers[HEADERS.X_SIGNATURE]?.toString();

    if (!timestamp || isNaN(timestamp) || !clientSignature) {
      throw new ValidationError(
        { timestamp: 'Invalid timestamp', signature: 'Missing signature' },
        'Missing or invalid timestamp/signature'
      );
    }

    const currentTimestamp = Date.now();
    const MAX_TIME_DIFF = 5 * 60 * 1000; // 5 minutes

    // Check timestamp validity
    if (Math.abs(currentTimestamp - timestamp) > MAX_TIME_DIFF) {
      throw new AuthenticationError('Request timestamp is invalid or expired');
    }

    // Prepare payload based on request method
    const payload = req.method === 'GET' 
      ? { ...req.query, timestamp }
      : { ...req.body, timestamp };

    const secretKey = process.env.SIGNATURE_SECRET_KEY || 'default-secret-key';
    const serverSignature = generateSignature(secretKey, timestamp.toString(), JSON.stringify(payload));

    // Compare signatures
    const clientSignatureBuffer = Buffer.from(clientSignature);
    const serverSignatureBuffer = Buffer.from(serverSignature);

    if (
      clientSignatureBuffer.length !== serverSignatureBuffer.length ||
      !crypto.timingSafeEqual(clientSignatureBuffer, serverSignatureBuffer)
    ) {
      throw new AuthenticationError('Invalid signature');
    }

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Bull Dashboard authentication middleware
 */
export const verifyBullDashboard = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const bullDashboardKey = req.cookies?.bull_dashboard_key;
    const expectedKey = process.env.BULL_DASHBOARD_KEY || 'oegjsc@1235';

    if (!bullDashboardKey || bullDashboardKey !== expectedKey) {
      throw new AuthorizationError('Access denied to Bull Dashboard');
    }

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Request timing middleware
 */
export const requestTimer = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  req.now = Date.now();
  next();
};

/**
 * Generate HMAC signature
 */
export const generateSignature = (
  secretKey: string,
  timestamp: string,
  payload: string = ''
): string => {
  const dataToSign = timestamp + payload;
  return crypto.createHmac('sha256', secretKey).update(dataToSign).digest('hex');
};

/**
 * Optional authentication middleware
 */
export const optionalAuth = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  // This middleware can be used for endpoints that work with or without authentication
  // It will attempt to authenticate but won't fail if authentication is missing
  
  const timestamp = req.headers[HEADERS.X_TIMESTAMP]?.toString();
  const clientSignature = req.headers[HEADERS.X_SIGNATURE]?.toString();

  if (timestamp && clientSignature) {
    // If auth headers are present, verify them
    verifyClient(req, res, next);
  } else {
    // If no auth headers, continue without authentication
    next();
  }
};
