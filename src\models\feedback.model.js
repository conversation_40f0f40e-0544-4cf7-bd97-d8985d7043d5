"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const feedbackModel = sequelize.define('feedbacks', {
    id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
    },
    fid: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    uid: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    pcn: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    feedback_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    feedback_title: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: true,
    },
}, {
    timestamps: false,
    underscored: true,
    tableName: 'feedbacks'
})

module.exports = feedbackModel