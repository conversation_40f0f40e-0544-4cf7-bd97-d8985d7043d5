import { Request, Response, NextFunction } from 'express';

// Base interfaces
export interface BaseResponse<T = any> {
  status: 'success' | 'error';
  code: number;
  message: string;
  metadata?: T;
  errors?: Record<string, string>;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface SortParams {
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface BaseQueryParams extends PaginationParams, SortParams {
  search?: string;
  status?: number;
}

// Extended Request interface
export interface AuthenticatedRequest extends Request {
  api_version?: number;
  user?: any;
  client?: any;
  now?: number;
}

// Handler types
export type AsyncHandler = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => Promise<void>;

export type ControllerMethod = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => Promise<void>;

// Database model base interface
export interface BaseModel {
  id: number;
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date | null;
  deleted_by?: number | null;
  status: number;
}

// API Version interface
export interface ApiVersion {
  id: number;
  client: string;
  api: number;
  status: number;
}

// Error types
export interface CustomError extends Error {
  status?: number;
  errors?: Record<string, string>;
}

// Redis cache interface
export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  key: string;
}

// Queue job interface
export interface QueueJob<T = any> {
  data: T;
  opts?: {
    removeOnComplete?: boolean;
    removeOnFail?: boolean;
    delay?: number;
  };
}

// Middleware options
export interface MiddlewareOptions {
  required?: boolean;
  skipValidation?: boolean;
}

// File upload interface
export interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  buffer: Buffer;
  size: number;
}

// Environment variables
export interface EnvConfig {
  NODE_ENV: string;
  APP_PORT: number;
  DB_HOST: string;
  DB_PORT: number;
  DB_NAME: string;
  DB_USER: string;
  DB_PASS: string;
  REDIS_HOST: string;
  REDIS_PORT: number;
  REDIS_PASSWORD?: string;
  SIGNATURE_SECRET_KEY: string;
  JWT_SECRET?: string;
}

// Service response interface
export interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: number;
}

// Repository interface
export interface BaseRepository<T> {
  findById(id: number): Promise<T | null>;
  findAll(conditions?: any): Promise<T[]>;
  create(data: Partial<T>): Promise<T>;
  update(id: number, data: Partial<T>): Promise<T | null>;
  delete(id: number): Promise<boolean>;
}

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors?: Record<string, string>;
  data?: any;
}
