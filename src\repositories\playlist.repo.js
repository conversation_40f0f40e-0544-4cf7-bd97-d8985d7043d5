"use strict";

const playlistModel = require("../models/playlist.model");

class PlaylistRepo {
    static async getListPlaylists(filter, include, attributes, order, limit) {
        return await playlistModel.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            order: order
        });
    }

    static async getListPlaylistsPagination(filter, attributes, order, page, limit) {
        const {count, rows} = await playlistModel.findAndCountAll({
            where: filter,
            attributes: attributes,
            limit: limit,
            offset: (page - 1) * limit,
            order: order
        });
        return {
            total_items: count,
            items_per_page: limit,
            total_pages: Math.ceil(count / limit),
            current_page: page,
            items: rows
        };
    }

    static async getPlaylistDetail(filter, include, attributes) {
        return await playlistModel.findOne({
            where: filter,
            include: include,
            attributes: attributes,
        });
    }

    static async createPlaylist(data) {
        const result = await playlistModel.create(data);
        return result.id;
    }

    static async updatePlaylist(model, data, filter = {}) {
        await model.update(data, {
            where: filter
        });
    }
}

module.exports = PlaylistRepo;