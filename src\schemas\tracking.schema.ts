import { z } from 'zod';

// Track link request schema
export const trackLinkSchema = z.object({
  link: z.string().url('Invalid link format'),
  type: z.string().min(1, 'Link type is required'),
  fid: z.number().int().positive().optional(),
});

// Track banner request schema
export const trackBannerSchema = z.object({
  banner_id: z.number().int().positive('Banner ID must be a positive integer'),
  fid: z.number().int().positive().optional(),
});

// Track video request schema
export const trackVideoSchema = z.object({
  video_id: z.number().int().positive('Video ID must be a positive integer'),
  fid: z.number().int().positive().optional(),
  duration: z.number().min(0).optional(),
});

// Type exports
export type TrackLinkRequest = z.infer<typeof trackLinkSchema>;
export type TrackBannerRequest = z.infer<typeof trackBannerSchema>;
export type TrackVideoRequest = z.infer<typeof trackVideoSchema>;
