"use strict";

const playlistVideoModel = require("../models/playlistVideo.model");

class PlaylistVideoRepo {
    static async createPlaylistVideo(data) {
        await playlistVideoModel.create(data);
    }

    static async createPlaylistVideosBulk(datas) {
        await playlistVideoModel.bulkCreate(datas);
    }

    static async deletePlaylistVideo(filter) {
        await playlistVideoModel.destroy({
            where: filter
        });
    }
}

module.exports = PlaylistVideoRepo;