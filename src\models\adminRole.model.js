"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const adminRoleModel = sequelize.define('admin_role', {
    admin_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    role_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
}, {
    tableName: 'admin_role',
    timestamps: false,
    underscored: true,
    freezeTableName: true,
    id: false,
});

module.exports = adminRoleModel;