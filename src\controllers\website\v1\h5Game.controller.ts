import { Response } from 'express';
import { BaseController } from '@/controllers/base.controller';
import { AuthenticatedRequest } from '@/types/common';
import { H5GameQueryParams, H5GameUserParams } from '@/schemas/h5Game.schema';
import { mockDatabase, simulateDbOperation } from '@/data/mockData';
import { H5Game, H5GameCategory } from '@/types/api';

export class H5GameController extends BaseController {
  /**
   * Get list of H5 games
   * GET /game-h5
   */
  public getListH5Games = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { page, limit, offset } = this.getPaginationParams(req);
      const { sortBy, sortOrder } = this.getSortParams(req);
      const search = this.getSearchParam(req);

      // Simulate database query with filters
      let h5Games = [...mockDatabase.h5Games];

      // Apply search filter
      if (search) {
        h5Games = h5Games.filter(game => 
          game.name.toLowerCase().includes(search.toLowerCase()) ||
          game.description?.toLowerCase().includes(search.toLowerCase())
        );
      }

      // Apply sorting
      h5Games.sort((a, b) => {
        const aValue = (a as any)[sortBy] || '';
        const bValue = (b as any)[sortBy] || '';
        
        if (sortOrder === 'ASC') {
          return aValue > bValue ? 1 : -1;
        }
        return aValue < bValue ? 1 : -1;
      });

      // Apply pagination
      const total = h5Games.length;
      const paginatedGames = h5Games.slice(offset, offset + limit);

      // Simulate async operation
      const result = await simulateDbOperation(paginatedGames);

      const response = this.buildPaginationResponse(result, total, page, limit);

      this.success(res, response, 'Get list h5 games successfully.');
    } catch (error) {
      this.error(res, 'Failed to get H5 games list');
    }
  };

  /**
   * Get list of H5 game categories
   * GET /game-h5/category
   */
  public getListH5GameCategories = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      // Mock H5 game categories
      const categories: H5GameCategory[] = [
        {
          id: 1,
          name: 'Action',
          slug: 'action',
          icon: 'https://example.com/icons/action.png',
          sort_order: 1,
          status: 1,
          created_at: new Date('2024-01-01'),
          updated_at: new Date('2024-01-01'),
        },
        {
          id: 2,
          name: 'Puzzle',
          slug: 'puzzle',
          icon: 'https://example.com/icons/puzzle.png',
          sort_order: 2,
          status: 1,
          created_at: new Date('2024-01-01'),
          updated_at: new Date('2024-01-01'),
        },
        {
          id: 3,
          name: 'Strategy',
          slug: 'strategy',
          icon: 'https://example.com/icons/strategy.png',
          sort_order: 3,
          status: 1,
          created_at: new Date('2024-01-01'),
          updated_at: new Date('2024-01-01'),
        },
      ];

      const result = await simulateDbOperation(categories);

      this.success(res, {
        categories: result,
        total: result.length
      }, 'Get list h5 game categories successfully.');
    } catch (error) {
      this.error(res, 'Failed to get H5 game categories');
    }
  };

  /**
   * Get list of trending H5 games
   * GET /game-h5/trending
   */
  public getListTrendingH5Games = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { page, limit, offset } = this.getPaginationParams(req);

      // Get trending games
      const trendingGames = mockDatabase.h5Games
        .filter(game => game.is_trending)
        .sort((a, b) => b.play_count - a.play_count);

      // Apply pagination
      const total = trendingGames.length;
      const paginatedGames = trendingGames.slice(offset, offset + limit);

      const result = await simulateDbOperation(paginatedGames);

      const response = this.buildPaginationResponse(result, total, page, limit);

      this.success(res, response, 'Get list trending h5 games successfully.');
    } catch (error) {
      this.error(res, 'Failed to get trending H5 games');
    }
  };

  /**
   * Get user H5 games by FID and UID
   * GET /game-h5/:fid/:uid
   */
  public getListUserH5Games = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { fid, uid } = req.params as unknown as H5GameUserParams;

      if (!fid || !uid) {
        this.badRequest(res, 'FID and UID are required');
        return;
      }

      const { page, limit, offset } = this.getPaginationParams(req);

      // Simulate getting user-specific H5 games
      // In real implementation, this would filter by user's played games, favorites, etc.
      const userGames = mockDatabase.h5Games
        .filter(game => game.play_count > 0) // Simulate user has played these games
        .sort((a, b) => b.play_count - a.play_count);

      // Apply pagination
      const total = userGames.length;
      const paginatedGames = userGames.slice(offset, offset + limit);

      const result = await simulateDbOperation(paginatedGames);

      const response = this.buildPaginationResponse(result, total, page, limit);

      this.success(res, response, 'Get list user h5 games successfully.');
    } catch (error) {
      this.error(res, 'Failed to get user H5 games');
    }
  };
}

export default new H5GameController();
