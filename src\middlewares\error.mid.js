"use strict";

const redisLogger = require("../loggers/redis.log");

const notFound = (req, res, next) => {
    const error = new Error('Not Found')
    error.status = 404
    next(error)
}

const errorHandler = async (error, req, res, next) => {
    console.log(error.stack)
    const statusCode = error.status || 500;
    const msg = error.message || 'Internal Server Error';
    const logMsg = `${statusCode}::${Date.now() - req.now}ms::${msg}`

    await redisLogger.error(req, logMsg, error)

    const response = {
        status: 'error',
        code: statusCode,
        message: msg,
    }
    if (error.errors) {
        response.errors = error.errors
    }

    return res.status(statusCode).json(response)
}

module.exports = {
    notFound,
    errorHandler
};
