"use strict";

const userGameReportModel = require("../models/userGameReport.model");

class UserGameReportRepo {
    static async getUserGameReportDetail(filter, attributes) {
        return await userGameReportModel.findOne({
            where: filter,
            attributes: attributes
        })
    }

    static async createUserGameReport(data) {
        return userGameReportModel.create(data);
    }

    static async updateUserGameReport(model, data) {
        await model.update(data);
    }
}

module.exports = UserGameReportRepo;