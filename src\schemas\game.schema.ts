import { z } from 'zod';

// Game query parameters schema
export const gameQuerySchema = z.object({
  fid: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined),
  category_id: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined),
  is_featured: z.string().optional().transform(val => val === 'true'),
  code: z.string().optional(),
  page: z.string().optional().transform(val => val ? parseInt(val, 10) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 20),
  search: z.string().optional(),
  status: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined),
});

// Create game request schema
export const createGameSchema = z.object({
  fid: z.number().int().positive('FID must be a positive integer'),
  data: z.array(
    z.object({
      code: z.string().min(1, 'Game code is required'),
      name: z.string().min(1, 'Game name is required'),
    })
  ).min(1, 'At least one game is required'),
});

// Open game request schema
export const openGameSchema = z.object({
  game_id: z.number().int().positive('Game ID must be a positive integer'),
  fid: z.number().int().positive().optional(),
  uid: z.number().int().positive().optional(),
});

// Game detail params schema
export const gameDetailParamsSchema = z.object({
  code: z.string().min(1, 'Game code is required'),
});

// Top games params schema
export const topGamesParamsSchema = z.object({
  fid: z.string().transform(val => parseInt(val, 10)),
  uid: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined),
});

// RSS request schema
export const rssRequestSchema = z.object({
  link: z.string().url('Invalid RSS link format'),
});

// Game response schemas
export const gameResponseSchema = z.object({
  id: z.number(),
  code: z.string(),
  name: z.string(),
  description: z.string().nullable(),
  icon: z.string().nullable(),
  thumbnail: z.string().nullable(),
  category_id: z.number().nullable(),
  is_featured: z.boolean(),
  play_count: z.number(),
  rating: z.number(),
  file_size: z.number().nullable(),
  version: z.string().nullable(),
  status: z.number(),
  created_at: z.date(),
  updated_at: z.date(),
});

export const gameListResponseSchema = z.object({
  games: z.array(gameResponseSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
});

// Type exports
export type GameQueryParams = z.infer<typeof gameQuerySchema>;
export type CreateGameRequest = z.infer<typeof createGameSchema>;
export type OpenGameRequest = z.infer<typeof openGameSchema>;
export type GameDetailParams = z.infer<typeof gameDetailParamsSchema>;
export type TopGamesParams = z.infer<typeof topGamesParamsSchema>;
export type RssRequest = z.infer<typeof rssRequestSchema>;
export type GameResponse = z.infer<typeof gameResponseSchema>;
export type GameListResponse = z.infer<typeof gameListResponseSchema>;
