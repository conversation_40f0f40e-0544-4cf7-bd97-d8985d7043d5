import { BaseModel, BaseQueryParams } from './common';

// Game related types
export interface Game extends BaseModel {
  code: string;
  name: string;
  description?: string;
  icon?: string;
  thumbnail?: string;
  category_id?: number;
  is_featured: boolean;
  play_count: number;
  rating: number;
  file_size?: number;
  version?: string;
}

export interface GameQueryParams extends BaseQueryParams {
  fid?: number;
  category_id?: number;
  is_featured?: boolean;
  code?: string;
}

export interface CreateGameRequest {
  fid: number;
  data: Array<{
    code: string;
    name: string;
  }>;
}

export interface OpenGameRequest {
  game_id: number;
  fid?: number;
  uid?: number;
}

// H5 Game related types
export interface H5Game extends BaseModel {
  name: string;
  slug: string;
  description?: string;
  thumbnail?: string;
  game_url: string;
  is_trending: boolean;
  play_count: number;
  rating: number;
}

export interface H5GameCategory extends BaseModel {
  name: string;
  slug: string;
  icon?: string;
  sort_order: number;
}

export interface H5GameQueryParams extends BaseQueryParams {
  fid?: number;
  uid?: number;
  category_id?: number;
  is_trending?: boolean;
}

// Banner related types
export interface Banner extends BaseModel {
  title: string;
  image: string;
  redirect_link?: string;
  position_id: number;
  start_date: Date;
  end_date: Date;
  sort_order: number;
  click_count: number;
}

export interface BannerV2 extends BaseModel {
  image: string;
  apply_date: Date;
  display_type: number;
  destination_type: number;
  active_click: boolean;
  sort?: number;
  redirect_link: any; // JSON field
}

export interface BannerQueryParams extends BaseQueryParams {
  fid?: number;
  position?: number;
  limit?: number;
}

// Video related types
export interface Video extends BaseModel {
  title: string;
  description?: string;
  thumbnail: string;
  video_url: string;
  duration: number;
  view_count: number;
  like_count: number;
  category_id?: number;
  is_featured: boolean;
}

export interface VideoQueryParams extends BaseQueryParams {
  category_id?: number;
  is_featured?: boolean;
}

// Playlist related types
export interface Playlist extends BaseModel {
  name: string;
  slug: string;
  icon?: string;
  description?: string;
  sort?: number;
}

// News related types
export interface News extends BaseModel {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  thumbnail?: string;
  author?: string;
  published_at: Date;
  view_count: number;
  is_featured: boolean;
  is_flash_news: boolean;
}

export interface NewsQueryParams extends BaseQueryParams {
  is_featured?: boolean;
  is_flash_news?: boolean;
  author?: string;
}

// Tracking related types
export interface TrackLinkRequest {
  link: string;
  type: string;
  fid?: number;
}

export interface TrackBannerRequest {
  banner_id: number;
  fid?: number;
}

export interface TrackVideoRequest {
  video_id: number;
  fid?: number;
  duration?: number;
}

// Location related types
export interface Location extends BaseModel {
  name: string;
  code: string;
  type: 'country' | 'region' | 'city';
  parent_id?: number;
  latitude?: number;
  longitude?: number;
}

// Setting related types
export interface AppSetting extends BaseModel {
  key: string;
  value: any; // JSON field
  fid: number;
  description?: string;
}

export interface SettingQueryParams {
  fid: number;
  keys?: string[];
}

// RSS related types
export interface RssRequest {
  link: string;
}

export interface RssItem {
  title: string;
  link: string;
  description?: string;
  published_at: Date;
  author?: string;
}

// FUS Game related types
export interface FusGame extends BaseModel {
  name: string;
  code: string;
  version: string;
  file_url: string;
  file_size: number;
  description?: string;
  changelog?: string;
  is_active: boolean;
}

export interface FusGameQueryParams {
  fid: number;
  version?: string;
}

// Client related types
export interface Client extends BaseModel {
  name: string;
  code: string;
  secret_key: string;
  allowed_domains: string[];
  is_active: boolean;
}

// Feedback related types
export interface SendFeedbackRequest {
  fid: number;
  uid: number;
  pcn: string;
  feedback_id: number;
  feedback_title: string;
  content?: string;
}
