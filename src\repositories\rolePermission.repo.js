"use strict";

const rolePermissionModel = require("../models/rolePermission.model");

class RolePermissionRepo {
    static async getListRolePermissions(filter, include, attributes) {
        return await rolePermissionModel.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            raw: true
        });
    }

    static async createRolePermission(data) {
        await rolePermissionModel.create(data);
    }

    static async deleteRolePermission(filter) {
        await rolePermissionModel.destroy({
            where: filter
        });
    }
}

module.exports = RolePermissionRepo;