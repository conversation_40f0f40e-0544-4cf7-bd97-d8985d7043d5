import { Response } from 'express';
import { BaseResponse } from '@/types/common';

export class ApiResponse {
  static success<T>(
    res: Response,
    data: T,
    message: string = 'Success',
    code: number = 200
  ): void {
    const response: BaseResponse<T> = {
      status: 'success',
      code,
      message,
      metadata: data,
    };
    res.status(code).json(response);
  }

  static error(
    res: Response,
    message: string = 'Internal Server Error',
    code: number = 500,
    errors?: Record<string, string>
  ): void {
    const response: BaseResponse = {
      status: 'error',
      code,
      message,
      errors,
    };
    res.status(code).json(response);
  }

  static badRequest(
    res: Response,
    message: string = 'Bad Request',
    errors?: Record<string, string>
  ): void {
    this.error(res, message, 400, errors);
  }

  static unauthorized(
    res: Response,
    message: string = 'Unauthorized'
  ): void {
    this.error(res, message, 401);
  }

  static forbidden(
    res: Response,
    message: string = 'Forbidden'
  ): void {
    this.error(res, message, 403);
  }

  static notFound(
    res: Response,
    message: string = 'Not Found'
  ): void {
    this.error(res, message, 404);
  }

  static conflict(
    res: Response,
    message: string = 'Conflict'
  ): void {
    this.error(res, message, 409);
  }

  static tooManyRequests(
    res: Response,
    message: string = 'Too Many Requests'
  ): void {
    this.error(res, message, 429);
  }
}

// Legacy compatibility class
export class OK<T> {
  private data: T;
  private message: string;
  private code: number;

  constructor(options: { message: string; metadata: T; code?: number }) {
    this.data = options.metadata;
    this.message = options.message;
    this.code = options.code || 200;
  }

  send(res: Response): void {
    ApiResponse.success(res, this.data, this.message, this.code);
  }
}

// Error classes for consistency
export class CustomError extends Error {
  public status: number;
  public errors?: Record<string, string>;

  constructor(message: string, status: number = 500, errors?: Record<string, string>) {
    super(message);
    this.status = status;
    this.errors = errors;
    this.name = this.constructor.name;
  }
}

export class BadRequestError extends CustomError {
  constructor(errors?: Record<string, string>, message: string = 'Bad Request') {
    super(message, 400, errors);
  }
}

export class UnauthorizedError extends CustomError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401);
  }
}

export class ForbiddenError extends CustomError {
  constructor(message: string = 'Forbidden') {
    super(message, 403);
  }
}

export class NotFoundError extends CustomError {
  constructor(message: string = 'Not Found') {
    super(message, 404);
  }
}

export class ConflictError extends CustomError {
  constructor(message: string = 'Conflict') {
    super(message, 409);
  }
}

export class TooManyRequestsError extends CustomError {
  constructor(message: string = 'Too Many Requests') {
    super(message, 429);
  }
}
