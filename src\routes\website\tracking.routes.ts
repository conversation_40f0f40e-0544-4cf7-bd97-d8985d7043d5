import { Router } from 'express';
import { asyncHandler } from '@/utils/asyncHandler';
import { validateBody } from '@/middlewares/validation.middleware';
import { verifyClient } from '@/middlewares/auth.middleware';
import {
  trackLinkSchema,
  trackBannerSchema,
  trackVideoSchema,
} from '@/schemas/tracking.schema';

const controllerName = 'tracking';
const router = Router();

/**
 * Tracking Routes - All require client verification
 */

// POST /tracking/link - Track link opening
router.post(
  '/link',
  verifyClient,
  validateBody(trackLinkSchema),
  asyncHandler(async (req, res, next) => {
    const { asyncVersionHandler } = await import('@/utils/asyncHandler');
    return asyncVersionHandler(controllerName, 'openLink')(req, res, next);
  })
);

// POST /tracking/banner - Track banner opening
router.post(
  '/banner',
  verifyClient,
  validateBody(trackBannerSchema),
  asyncHand<PERSON>(async (req, res, next) => {
    const { asyncVersionHandler } = await import('@/utils/asyncHandler');
    return asyncVersionHandler(controllerName, 'openBanner')(req, res, next);
  })
);

// POST /tracking/video - Track video opening
router.post(
  '/video',
  verifyClient,
  validateBody(trackVideoSchema),
  asyncHandler(async (req, res, next) => {
    const { asyncVersionHandler } = await import('@/utils/asyncHandler');
    return asyncVersionHandler(controllerName, 'openVideo')(req, res, next);
  })
);

export default router;
