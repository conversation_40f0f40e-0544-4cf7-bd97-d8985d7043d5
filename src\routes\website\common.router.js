"use strict";

const express = require('express');
const newsController = require("../../controllers/website/v1/news.controller");
const {rssRequest, openGameRequest} = require("../../requests/website/game.request");
const {asyncHandler, asyncVersionHandler} = require("../../helpers");
const {sendFeedbackRequest} = require("../../requests/website/feedback.request");
const {verifyClient, verifyClientV2} = require("../../middlewares/website/verifyClient.mid");
const {sequelize, logDB} = require("../../dbs/mysql.db");

const router = express.Router()

router.get('/health-check', [verifyClientV2, async (req, res) => {
    try {
        // await sequelize.authenticate();
        // await logDB.authenticate();
        await sequelize.query('SELECT 1');
        await logDB.query('SELECT 1');
        return res.status(200).json({ status: 'success', message: 'Connected' });
    } catch (err) {
        console.error('DB Health Check Error:', err.message);
        return res.status(500).json({ status: 'error', db: 'Disconnected' });
    }
}]);

router.get('/update-rss', [asyncHandler(newsController.updateRss)]);
router.post('/update-rss', [asyncHandler(newsController.updateRss)]);

router.get('/rss', [rssRequest, asyncVersionHandler('game', 'getListRss')]);

router.get('/app-settings/:fid', asyncVersionHandler('setting', 'getListSettings'));
router.get('/fus-game/:fid', asyncVersionHandler('fusGame', 'getFusGameDetail'));
router.get('/location', [verifyClient, asyncVersionHandler('location', 'getLocationDetail')]);

// news
// router.use('/feedback', [sendFeedbackRequest, asyncHandler(feedbackController.sendFeedback)]);

module.exports = router;