"use strict";

const regionModel = require("../models/region.model");

class RegionRepo {
    static async getListRegions(filter, attributes, order, limit) {
        return await regionModel.findAll({
            where: filter,
            limit: limit,
            order: order,
            attributes: attributes
        })
    }

    static async getListRegionsPagination(filter, include, attributes, order, page, limit) {
        const {count, rows} = await regionModel.findAndCountAll({
            where: filter,
            include: include,
            attributes: attributes,
            order: order,
            offset: (page - 1) * limit,
            limit: limit
        })

        return {
            total_items: count,
            items_per_page: limit,
            current_page: page,
            total_pages: Math.ceil(count / limit),
            items: rows
        };
    }

    static async getRegionDetail(filter, include, attributes) {
        return await regionModel.findOne({
            where: filter,
            include: include,
            attributes: attributes
        })
    }

    static async createRegion(data) {
        const result = await regionModel.create(data);
        return result.id;
    }

    static async updateRegion(model, data, filter = {}) {
        await model.update(data, {
            where: filter
        });
    }

    static async updateRegionWithConditions(data, filter = {}) {
        await regionModel.update(data, {
            where: filter
        });
    }
}

module.exports = RegionRepo;