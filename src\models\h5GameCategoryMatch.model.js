"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const h5GameCategoryMatchModel = sequelize.define('h5_game_category', {
    game_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
    category_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
    },
}, {
    id: false,
    timestamps: false,
    underscored: true,
    tableName: 'h5_game_category'
})

module.exports = h5GameCategoryMatchModel