"use strict";

const express = require('express');
const {asyncVersionHandler} = require("../../helpers");
const {newGameRequest} = require("../../requests/website/game.request");

const router = express.Router();

const controllerName = 'game'

router.get('/', [asyncVersion<PERSON>andler(controllerName, 'getListGames')]);
router.post('/', [newGameRequest, asyncVersionHandler(controllerName, 'createGame')]);
router.get('/top/:fid', asyncVersionHandler(controllerName, 'getListTopGames'));
router.get('/top/:fid/:uid', asyncVersionHandler(controllerName, 'getListUserGames'));
router.get('/:code', asyncVersionHandler(controllerName, 'getGameDetail'));
router.post('/open', [asyncVersion<PERSON>and<PERSON>(controllerName, 'openGame')]);

module.exports = router;