"use strict";

const multer = require('multer');
const rateLimit = require("express-rate-limit");
const RedisStore = require("rate-limit-redis").default;
const {TooManyRequestError} = require("../utils/errorResponse");
const {redisClient} = require("../dbs/ioredis.db");

const corsOptions = {
    origin: (origin, callback) => {
        const allowedDomains = [
            /^https:\/\/.*\.oeg\.vn$/,
            "https://talenthouse.vn",
            "https://dev.talenthouse.vn",

            "http://localhost:5173", // local
            "http://localhost:3002", // local

            "http://127.0.0.1:55808", // FUS test
            "http://localhost:55808", // FUS test
            "https://menu.fusoft.vn",  // FUS test

            "https://dev-cms.fusoft.vn",
            "https://dev-client-cms.fusoft.vn"
        ];

        const isAllowed = !origin || allowedDomains.some(pattern =>
            typeof pattern === "string"
                ? pattern === origin
                : pattern.test(origin)
        );

        if (isAllowed) {
            callback(null, true);
        } else {
            callback(new Error("Not allowed by CORS"));
        }
    },
    credentials: true, // Cho phép gửi cookie từ trình duyệt của khách hàng
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], // Các phương thức HTTP được cho phép
    // allowedHeaders: ['Content-Type', 'Authorization'],
    preflightContinue: false,
    optionsSuccessStatus: 204
};

// const limiter = (rateLimit) => {
//     return rateLimit({
//         windowMs: 60 * 1000,
//         max: 500,
//         standardHeaders: true,
//         keyGenerator: (req, res) => req.ip,
//         handler: (req, res) => {
//             TooManyRequestError('Too many requests, please try again later!');
//         }
//     })
// };

const limiter = rateLimit({
    store: new RedisStore({
        sendCommand: (...args) => redisClient.call(...args),
        prefix: `${redisClient.options.keyPrefix}rate-limit:`,  // Thêm prefix để tránh xung đột key Redis
        expiry: 60,             // Thời gian hết hạn của key rate limit
    }),
    windowMs: 60 * 1000, // 1 phút
    max: (req) => {
        return req.headers.authorization ? 200 : 500;
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        const ip = req.headers['x-forwarded-for'] ? req.headers['x-forwarded-for'] : req.ip;
        return req.headers.authorization ? req.headers.authorization.split(' ')[1] : ip; // Ưu tiên giới hạn theo ADMIN ID
    },
    handler: (req, res) => {
        TooManyRequestError('Too many requests, please try again later!');
    },
    trustProxy: true
});

const storage = multer.memoryStorage();
const uploadFile = multer({storage: storage})

module.exports = {
    corsOptions,
    limiter,
    uploadFile
}