"use strict";

const bannerPositionModel = require("../models/bannerPosition.model");

class BannerPositionRepo {
    static async getListBannerPositions(filter, include, attributes, order, limit) {
        return await bannerPositionModel.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            order: order
        });
    }

    static async getListBannerPositionsPagination(filter, attributes, order, page, limit) {
        const {count, rows} = await bannerPositionModel.findAndCountAll({
            where: filter,
            attributes: attributes,
            limit: limit,
            offset: (page - 1) * limit,
            order: order
        });
        return {
            total_items: count,
            items_per_page: limit,
            total_pages: Math.ceil(count / limit),
            current_page: page,
            items: rows
        };
    }

    static async getBannerPositionDetail(filter, include, attributes) {
        return await bannerPositionModel.findOne({
            where: filter,
            include: include,
            attributes: attributes,
        });
    }

    static async createBannerPosition(data) {
        const result = await bannerPositionModel.create(data);
        return result.id;
    }

    static async updateBannerPosition(model, data) {
        await model.update(data);
    }
}

module.exports = BannerPositionRepo;