"use strict";

const {DataTypes} = require('sequelize');
const {sequelize} = require('../dbs/mysql.db');

const bannersV2Model = sequelize.define('banners_v2', {
    id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
    },
    region: {
        type: DataTypes.TEXT,
        allowNull: true,
    },
    city: {
        type: DataTypes.TEXT,
        allowNull: true,
    },
    fid: {
        type: DataTypes.TEXT,
        allowNull: true,
    },
    position_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    client_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    client_product_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    image: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    apply_date: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    apply_start_date: {
        type: DataTypes.DATE,
        allowNull: true,
    },
    apply_end_date: {
        type: DataTypes.DATE,
        allowNull: true,
    },
    apply_time_slots: {
        type: DataTypes.TEXT,
        allowNull: true,
    },
    display_type: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    destination_type: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    redirect_link: {
        type: DataTypes.TEXT,
        allowNull: true,
    },
    active_click: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    apply_to: {
        type: DataTypes.TEXT,
        allowNull: false,
    },
    location_type: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    location_value: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    sort: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    status: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
    },
    created_role: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: true,
    },
    updated_role: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    deleted_at: {
        type: DataTypes.DATE,
        allowNull: true,
    },
    deleted_role: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    deleted_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
    }
}, {
    timestamps: false,
    underscored: true,
    defaultScope: {
        attributes: {exclude: ['updated_at', 'updated_by', 'deleted_at', 'deleted_by']}
    },
    tableName: 'banners_v2'
});

module.exports = bannersV2Model;