"use strict";

const h5GameCategoryMatchModel = require("../models/h5GameCategoryMatch.model");

class H5GameCategoryRepo {
    static async getListH5GameCategory(filter, include, attributes) {
        return await h5GameCategoryMatchModel.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            raw: true
        });
    }

    static async createH5GameCategory(data) {
        await h5GameCategoryMatchModel.create(data);
    }

    static async deleteH5GameCategory(filter) {
        await h5GameCategoryMatchModel.destroy({
            where: filter
        });
    }
}

module.exports = H5GameCategoryRepo;