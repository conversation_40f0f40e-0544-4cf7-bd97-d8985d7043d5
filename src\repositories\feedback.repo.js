"use strict";

const FeedbackModel = require("../models/feedback.model");

class feedbackRepo {
    static async getListFeedbacks(filter, attributes, order, limit) {
        return await FeedbackModel.findAll({
            where: filter,
            limit: limit,
            order: order,
            attributes: attributes
        })
    }

    static async getListFeedbacksPagination(filter, attributes, order, page, limit) {
        const {count, rows} = await FeedbackModel.findAndCountAll({
            where: filter,
            attributes: attributes,
            order: order,
            offset: (page - 1) * limit,
            limit: limit
        })

        return {
            total_items: count,
            items_per_page: limit,
            current_page: page,
            total_pages: Math.ceil(count / limit),
            items: rows
        };
    }

    static async createFeedback(data) {
        const result = await FeedbackModel.create(data);
        return result.id;
    }
}

module.exports = feedbackRepo;