"use strict";

const {OK} = require("../../../utils/successResponse");
const fusGameService = require("../../../services/website/v1/fusGame.service");

class GameController {
    getFusGameDetail = async (req, res, next) => {
        new OK({
            message: 'Get fus game detail successfully.',
            metadata: await fusGameService.getFusGameDetail(req)
        }).send(res)
    }
}

module.exports = new GameController();