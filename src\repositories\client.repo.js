"use strict";

const clientModel = require("../models/client.model");

class clientRepo {
    static async getListClients(filter, include, attributes, order, limit) {
        return await clientModel.findAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            order: order
        });
    }

    static async getListClientsPagination(filter, include, attributes, order, page, limit) {
        const {count, rows} = await clientModel.findAndCountAll({
            where: filter,
            include: include,
            attributes: attributes,
            limit: limit,
            offset: (page - 1) * limit,
            order: order,
            distinct: true
        });

        return {
            total_items: count,
            items_per_page: limit,
            total_pages: Math.ceil(count / limit),
            current_page: page,
            items: rows
        };
    }

    static async getClientDetail(filter, include = null, attributes = []) {
        return await clientModel.findOne({
            where: filter,
            include: include,
            attributes: attributes
        })
    }

    static async createClient(data) {
        const result = await clientModel.create(data);
        return result.id;
    }

    static async updateClient(model, data) {
        await model.update(data);
    }
}

module.exports = clientRepo;